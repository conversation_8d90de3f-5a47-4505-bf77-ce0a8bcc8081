using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TRF3.SISPREC.Migrations
{
    /// <inheritdoc />
    public partial class RecriandoTabelaDocumento : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ANA_MODELO_DOCUMENTO");

            migrationBuilder.CreateTable(
                name: "ANA_MODELO_DOCUMENTO",
                columns: table => new
                {
                    SEQ_MODELO_DOCUME = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SEQ_SETOR = table.Column<int>(type: "int", nullable: false),
                    NOM_MODELO_DOCUME = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false),
                    TXT_MODELO_DOCUME = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    SIN_EXCLUI = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("ANA_MODELO_DOCUMENTO_P01", x => x.SEQ_MODELO_DOCUME);
                    table.ForeignKey(
                        name: "ANA_MODELO_DOCUMENTO_R01",
                        column: x => x.SEQ_SETOR,
                        principalTable: "TRF_SETOR",
                        principalColumn: "SEQ_SETOR");
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ANA_MODELO_DOCUMENTO");

            migrationBuilder.CreateTable(
               name: "ANA_MODELO_DOCUMENTO",
               columns: table => new
               {
                   SEQ_MODELO_DOCUME = table.Column<int>(type: "int", nullable: false)
                       .Annotation("SqlServer:Identity", "1, 1"),
                   SEQ_SETOR = table.Column<int>(type: "int", nullable: false),
                   SIN_EXCLUI = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                   NOM_MODELO_DOCUME = table.Column<string>(type: "varchar(255)", maxLength: 255, nullable: false),
                   TXT_MODELO_DOCUME = table.Column<string>(type: "nvarchar(max)", nullable: false)
               },
               constraints: table =>
               {
                   table.PrimaryKey("ANA_MODELO_DOCUMENTO_P01", x => x.SEQ_MODELO_DOCUME);
                   table.ForeignKey(
                       name: "ANA_MODELO_DOCUMENTO_R01",
                       column: x => x.SEQ_SETOR,
                       principalTable: "TRF_SETOR",
                       principalColumn: "SEQ_SETOR");
               });
        }
    }
}
