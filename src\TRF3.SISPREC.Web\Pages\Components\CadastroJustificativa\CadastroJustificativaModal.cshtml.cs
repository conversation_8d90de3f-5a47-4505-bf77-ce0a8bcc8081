using Microsoft.AspNetCore.Mvc;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicaoJustificativas.Dtos;
using TRF3.SISPREC.JustificativaDocumentos;

namespace TRF3.SISPREC.Web.Pages.Components.CadastroJustificativas
{
    [ExcludeFromCodeCoverage]
    public class CadastroJustificativaModal : SISPRECPageModel
    {
        private readonly IRequisicaoJustificativaAppService _requisicaoJustificativaAppService;

        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public string? Id { get; set; }

        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public string? Procedimento { get; set; }

        [HiddenInput]
        [BindProperty(SupportsGet = true)]
        public EDescricaoAnaliseTela TipoAnalise { get; set; }

        [BindProperty]
        public CadastroJutificativaViewModel ViewModel { get; set; } = new();

        public CadastroJustificativaModal(IRequisicaoJustificativaAppService requisicaoJustificativaAppService)
        {
            _requisicaoJustificativaAppService = requisicaoJustificativaAppService;
        }

        public virtual async Task OnGetAsync()
        {
            ViewModel.RequisicaoList = Id!.Split(',').ToList();
            ViewModel.Procedimento = Procedimento;
            ViewModel.TipoAnalise = TipoAnalise.GetEnumDescription();
            ViewModel.MotivoList =
            [
                new(string.Empty, null)
            ];

            await Task.CompletedTask;
        }

        public virtual async Task<IActionResult> OnPostAsync()
        {
            if (ViewModel.TipoAnalise == EDescricaoAnaliseTela.CPF_CNPJ.GetEnumDescription() && (int.Parse(ViewModel.Decisao!) == (int)EDescricaoAcaoTipo.CANCELAMENTO && ViewModel.ExtratoRFB == null))
                ModelState.AddModelError("ExtratoRFB", "O Extrato da RFB é obrigatório.");
            else if (ViewModel.TipoAnalise == EDescricaoAnaliseTela.CPF_CNPJ.GetEnumDescription() && (int.Parse(ViewModel.Decisao!) == (int)EDescricaoAcaoTipo.LIBERACAO && ViewModel.ExtratoRFB != null))
                ModelState.AddModelError("ExtratoRFB", "O Extrato da RFB não deve ser informado.");
            else if (ViewModel.TipoAnalise != EDescricaoAnaliseTela.CPF_CNPJ.GetEnumDescription() && ViewModel.ExtratoRFB != null)
                ModelState.AddModelError("ExtratoRFB", "O Extrato da RFB não deve ser informado.");

            ValidateModel();

            var input = new CreateUpdateJustificativaComplementoDto
            {
                Motivo = ViewModel.Motivo,
                ComplementoMotivo = ViewModel.ComplementoMotivo,
                Observacoes = ViewModel.Observacoes,
                RequisicaoList = ViewModel.RequisicaoList,
                RequisicoesComparadas = ViewModel.RequisicoesComparadas,
                TipoAnalise = TipoAnalise,
                ExtratoRFB = ViewModel.ExtratoRFB,
                Procedimento = ViewModel.Procedimento,
                Decisao = (EDescricaoAcaoTipo)int.Parse(ViewModel.Decisao!)
            };

            await _requisicaoJustificativaAppService.CreateJustificativaCompletoAsync(input);

            return NoContent();
        }
    }
}