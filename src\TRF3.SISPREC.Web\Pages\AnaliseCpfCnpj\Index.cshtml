@page
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@model TRF3.SISPREC.Web.Pages.AnaliseCpfCnpj.IndexModel
@inject IPageLayout PageLayout
@{
    PageLayout.Content.Title = "Análise de CPF/CNPJ";

    ViewData["Meses"] = Model.MesesComItemVazio;
}

@section scripts
{
    <abp-script src="/Pages/AnaliseCpfCnpj/index.js" />
    <abp-script src="/js/componente-utils.js" />
}

@section styles
{
    <abp-style src="/css/app/analises.css" />
}

<abp-card>
    <abp-card-body>

        <!--BOTÃO RECOLHER/EXPANDIR-->
        <div class="tooltip-container float-end" style="margin:-1.5rem">
            <abp-button class="ocultar-secao-botao" id="btnOcultarSecaoTopo" size="Small" button-type="Default" abp-collapse-id="ocultarSecaoTopo">ᐱ</abp-button>
            <span class="tooltiptext" id="tooltipText">Recolher</span>
        </div>

        <!--SEÇÃO OCULTAR E EXIBIR-->
        <abp-collapse-body id="ocultarSecaoTopo" show="true">
            <form asp-for="AnaliseCpfCnpjFilterInput" id="AnaliseCpfCnpjFilterInput">
                <partial name="Shared/Partials/_AnaliseFiltroForm" model="@Model.AnaliseCpfCnpjFilterInput" />
            </form>

            <!--LISTAS-->
            <abp-row class="lista-dados">
                <abp-column style="max-width: 50%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="CpfCnpjRequisicoesTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
                <abp-column style="max-width: 50%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="PartesTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
            </abp-row>
        </abp-collapse-body>

        <!--BOTÕES-->
        <abp-row class="d-flex button-container barra-navegacao">

            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="CpfCnpjRequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="width: 135px; margin: -6px;">
                <abp-input size="Small" asp-for="ViewModel.NumeroDaRequisicao" readonly="true" />
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="CpfCnpjRequisicoesTable"></abp-button>
            </abp-column>

            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="PartesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="width: 135px; margin: -6px;">
                <abp-input size="Small" asp-for="ViewModel.CpfCnpjParteAnalisada" readonly="true" />
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="PartesTable"></abp-button>
            </abp-column>

            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Detalhes da Requisição" id="btnDetalhesDaRequisicao"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="padding: 0px;">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Consultar RFB" id="btnConsultarRfb"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="padding: 0px;">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Cadastrar Justificativa" id="btnCadastrarJustificativa" data-tipo-analise="1" />
            </abp-column>
        </abp-row>

        <abp-row>
            <abp-column>
                <abp-input asp-for="ViewModel.NomeSistema" readonly="true" />
            </abp-column>
            <abp-column>
                <abp-input asp-for="ViewModel.SituacaoCadastral" readonly="true" />
            </abp-column>
        </abp-row>

        <abp-row>
            <abp-column>
                <abp-input asp-for="ViewModel.NomeConselho" readonly="true" />
            </abp-column>
            <abp-column>
                <abp-input asp-for="ViewModel.NomeJuizo" readonly="true" />
            </abp-column>
        </abp-row>

        <abp-row class="align-items-center">
            <abp-column size="_6">
                <input-colar asp-for="ViewModel.NomeReceita" />
            </abp-column>

            <abp-column>
                <abp-input asp-for="ViewModel.Alvara" disabled />
            </abp-column>
        </abp-row>

        <abp-row>
            <abp-input asp-for="ViewModel.Observacao" readonly="true" />
        </abp-row>
    </abp-card-body>

</abp-card>