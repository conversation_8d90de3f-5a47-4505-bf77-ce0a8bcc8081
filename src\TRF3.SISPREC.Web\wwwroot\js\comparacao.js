/**
 * Executa a comparação de valores entre elementos HTML e adiciona uma classe se os valores forem iguais.
 * 
 * @param {string} seletor - Seletor para buscar os elementos na DOM (ex: '[id^="td"], select[id^="combo"]').
 * @param {string} classeAdicionar - Classe CSS a ser adicionada quando os valores forem iguais (ex: "destaque-amarelo").
 */
function executarComparacao(seletor, classeAdicionar) {
    const serviceComparar = tRF3.sISPREC.analiseCpfCnpj.analiseCpfCnpj;

    // Remove a classe de todos os elementos antes de iniciar a comparação
    document.querySelectorAll(`.${classeAdicionar}`).forEach(el => el.classList.remove(classeAdicionar));

    // Captura os elementos com base no seletor fornecido
    const valores = [...document.querySelectorAll(seletor)].map(el => {
        const linha = el.closest('tr');
        const isPrincipal = linha?.className.includes('requisicao-principal');
        const isComparada = linha?.className.includes('requisicao-comparada');

        return {
            id: $(el).attr('name'),
            valor: el.tagName === "OPTION" ? el.textContent.trim() : el.textContent.trim() || el.value,
            elemento: el,
            isPrincipal,
            isComparada
        };
    });

    const idMap = new Map(); // Mapa para armazenar os valores já encontrados
    valores.forEach(item => {
        if (!item.valor) return;

        if (idMap.has(item.id)) {
            const existente = idMap.get(item.id);
            abp.ui.block({ elm: 'body', busy: true });
            serviceComparar.compararNomes(existente.valor, item.valor)
                .then(iguais => {
                    if (iguais) {
                        item.elemento.classList.add(classeAdicionar);
                    }
                })
                .catch(() => {
                    abp.message.error('Ocorreu um erro ao tentar comparar os dados.');
                })
                .always(() => {
                    abp.ui.unblock({ elm: 'body' });
                });
        } else {
            idMap.set(item.id, item); // Armazena o primeiro valor do ID
        }
    });
}

function executarComparacaoPersonalizada() {

    const mapeamentoCampos = [
        {
            campoDestaque: { name: "tdProcedimento", class: "requisicao-principal" },
            campoComparar: { name: "tdProcedimento", class: "requisicao-comparada" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-vermelho" }
        },
        {
            campoDestaque: { name: "tdNatureza", class: "requisicao-principal" },
            campoComparar: { name: "tdNatureza", class: "requisicao-comparada" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-amarelo" }
        },
        {
            campoDestaque: { name: "tdCpfCnpjAutor", class: "requisicao-principal" },
            campoComparar: { name: "tdCpfCnpjAutor", class: "requisicao-comparada" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-amarelo" }
        },
        {
            campoDestaque: { name: "tdParteAutora", class: "requisicao-principal" },
            campoComparar: { name: "tdParteAutora", class: "requisicao-comparada" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-amarelo" }
        },
        {
            campoDestaque: { name: "tdCodigoTipoIndicadorEconomico", class: "requisicao-principal" },
            campoComparar: { name: "tdCodigoTipoIndicadorEconomico", class: "requisicao-comparada" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-laranja" }
        },
        {
            campoDestaque: { name: "tdValorFormatadoCnpjBeneficiario", class: "requisicao-principal" },
            campoComparar: { name: "tdValor", class: "requisicao-principal" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-laranja" }
        },
        {
            campoDestaque: { name: "tdDataContaFormatadoCnpjBeneficiario", class: "requisicao-principal" },
            campoComparar: { name: "tdDataConta", class: "requisicao-principal" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-laranja" }
        },
        {
            campoDestaque: { name: "tdCpfCnpjBeneficiario", class: "requisicao-principal" },
            campoComparar: { name: "tdCpfCnpjRequerente", class: "requisicao-comparada" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-amarelo" }
        },
        {
            campoDestaque: { name: "tdNomeBeneficiario", class: "requisicao-principal" },
            campoComparar: { name: "tdRequerente", class: "requisicao-comparada" },
            classeAdicionar: { igual: "destaque-verde", diferente: "destaque-amarelo" }
        }
    ];

    const serviceComparar = tRF3.sISPREC.analiseCpfCnpj.analiseCpfCnpj;

    mapeamentoCampos.forEach(mapeamento => {
        abp.ui.block({ elm: 'body', busy: true });
        let $campoDestaque = $(`.${mapeamento.campoDestaque.class} [name=${mapeamento.campoDestaque.name}]`);
        let valorCampoDestaque = $campoDestaque.text();
        let valorCampoComparar = $(`.${mapeamento.campoComparar.class} [name=${mapeamento.campoComparar.name}]`).text();

        if (valorCampoDestaque && valorCampoComparar) {
            serviceComparar.compararNomes(valorCampoDestaque, valorCampoComparar)
                .then(iguais => {
                    let classe = iguais ? mapeamento.classeAdicionar.igual : mapeamento.classeAdicionar.diferente;
                    $campoDestaque.addClass(classe);
                })
                .catch(() => {
                    abp.message.error('Ocorreu um erro ao tentar comparar os dados.');
                })
                .always(() => {
                    abp.ui.unblock({ elm: 'body' });
                });
        }
    });
}
