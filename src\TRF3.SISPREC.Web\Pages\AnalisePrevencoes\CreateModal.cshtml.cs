using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.AnalisePrevencoes;

[ExcludeFromCodeCoverage]
public class CreateModalModel : SISPRECPageModel
{
    [HiddenInput]
    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    [BindProperty]
    [Display(Name = "Observação para geração do espelho (opcional)")]
    [TextArea(Rows = 4)]
    [FormControlSize(AbpFormControlSize.Large)]
    public string? ObservacaoGeracaoEspelho { get; set; }

    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }

}