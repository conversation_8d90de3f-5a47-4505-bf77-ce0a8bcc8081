$(function () {
    const service = tRF3.sISPREC.analisePendencias.analisePendencia;

    let requisicoesDataTable = inicializarRequisicoesDataTable();

    const porCpfDataTable = inicializarPorCpfDataTable();

    const porOriginarioDataTable = inicializarPorOriginarioDataTable();

    const porEstornadaDataTable = inicializarPorEstornadaDataTable();

    const ocorrenciasDataTable = inicializarOcorrenciasDataTable();

    const tables = {
        "#ListaPorCPF": "#containerProtocolosPorCpf",
        "#ListaPorOriginario": "#containerProtocolosPorOriginario",
        "#ListaPorEstornada": "#containerProtocolosPorEstornada"
    };

    const createModal = new abp.ModalManager(abp.appPath + 'AnalisePrevencoes/CreateModal');

    createModal.onResult(function () {
        let numeroRequisicao = $('#ViewModel_NumeroRequisicaoComparada').val().trim();

        if (numeroRequisicao) {
            let observacaoEspelho = $('#ObservacaoGeracaoEspelho').val();
            window.listaExportacaoObservacao.set(numeroRequisicao, observacaoEspelho);
        }
    });

    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'Ano', 'Mes');

    $('#btnPesquisar').on('click', function (event) {
        event.preventDefault();
        novaPesquisa();
    });

    $('#ListaPorCPF').on('click', function (event) {
        event.preventDefault();
        showTable('#ListaPorCPF');
    });

    $('#ListaPorOriginario').on('click', function (event) {
        event.preventDefault();
        showTable('#ListaPorOriginario');
    });

    $('#ListaPorEstornada').on('click', function (event) {
        event.preventDefault();
        showTable('#ListaPorEstornada');
    });

    $('#btnSalvarComparada').on('click', function (e) {
        e.preventDefault();
        let numeroRequisicao = $('#ViewModel_NumeroRequisicaoComparada').val().trim();

        if (numeroRequisicao) {
            let observacaoEspelho = window.listaExportacaoObservacao.get(numeroRequisicao);

            createModal.open();
            setTimeout(() => {
                let modal = document.querySelector(".modal"); // Ajuste para a classe/campo do modal
                let inputElement = modal?.querySelector(".custom-textarea"); // Busca dentro do modal

                if (inputElement) {
                    inputElement.value = observacaoEspelho ?? '';
                    inputElement.dispatchEvent(new Event("input")); // Se for um componente reativo
                }
            }, 300);
        }
    });

    $('#btnOcultarSecaoTopo').on('click', function () {
        $.fn.dataTable.tables({ "filter": '.lista-dados', "api": true }).columns.adjust();
    });

    $(document).on('cadastroJustificativa:updated', function () {
        abp.notify.success('Salvo com sucesso!');
        novaPesquisa();
    });

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function (event, jqXHR) {
        // Garante que a UI será desbloqueada
        abp.ui.unblock();

        resetRequisicoesDataTable();
    });

    function inicializarRequisicoesDataTable() {
        return $('#PendenciaRequisicoesTable')
            // Bloqueia a tela antes de enviar a requisição ajax.
            .on('preXhr.dt', function () { abp.ui.block({ elm: 'body', busy: false }); })
            // Intercepta o evento após a requisição (sucesso ou erro) --> só está disparando após sucesso. Talvez o abp.libs.datatables.createAjax() esteja "tratando" o erro?
            .on('xhr.dt', function () { abp.ui.unblock(); })
            // Desbloqueia a tela em caso de erro (ex: timeout).
            .on('error.dt', function () { abp.ui.unblock(); })
            // Configura o datatable.
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                processing: true,
                serverSide: true,
                deferLoading: 0, // Delay the loading of server-side data until second draw.
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'numeroProtocoloRequisicaoPendente',
                ajax: abp.libs.datatables.createAjax(service.buscarRequisicoesAnalisesPendentes, obterFiltrosPesquisa),
                columnDefs: [
                    {
                        title: "Requisição",
                        data: "numeroProtocoloRequisicaoPendente"
                    }
                ],
                drawCallback: function (settings) {
                    let tabela = this.api();
                    // Seleciona a primeira linha.
                    selecionarLinhaTabela(tabela, 0);
                }
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarRequisicaoEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="PendenciaRequisicoesTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="PendenciaRequisicoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function inicializarPorCpfDataTable() {
        return $('#ProtocolosPorCpfTable')
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'numeroProtocoloRequisicaoPendente',
                columnDefs: [
                    {
                        title: "Comparar por CPF",
                        data: "numeroProtocoloRequisicaoPendente"
                    }
                ]
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarRequisicaoPorCpfEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="ProtocolosPorCpfTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="ProtocolosPorCpfTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function inicializarPorOriginarioDataTable() {
        return $('#ProtocolosPorOriginarioTable')
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'numeroProtocoloRequisicaoPendente',
                columnDefs: [
                    {
                        title: "Comparar por Originário",
                        data: "numeroProtocoloRequisicaoPendente"
                    }
                ]
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarRequisicaoPorOriginarioEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="ProtocolosPorOriginarioTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="ProtocolosPorOriginarioTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function inicializarPorEstornadaDataTable() {
        return $('#ProtocolosPorEstornadaTable')
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'numeroProtocoloRequisicaoPendente',
                columnDefs: [
                    {
                        title: "Comparar por Estornadas",
                        data: "numeroProtocoloRequisicaoPendente"
                    }
                ]
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarRequisicaoPorEstornadaEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="ProtocolosPorEstornadaTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="ProtocolosPorEstornadaTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function inicializarOcorrenciasDataTable() {
        return $('#OcorrenciasTable')
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                columnDefs: [
                    {
                        title: "Tipo de Ocorrência",
                        data: "descricaoAnaliseTela"
                    },
                    {
                        title: "Detalhe",
                        data: "descricaoMotivoOcorrencia"
                    }
                ]
            }));
    }

    function obterFiltrosPesquisa() {
        const input = {};

        $('#AnalisePendenciasFilterInput')
            .serializeArray()
            .forEach(function (data) {
                if (data.value !== '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnalisePendenciasFilterInput./g, ''))] = data.value;
                }
            });
        return input;
    };

    function obterRequisicaoEmAnalise() { return requisicoesDataTable.row({ selected: true }).id(); };

    function obterPendenciaPorCpfEmAnalise() { return porCpfDataTable.row({ selected: true }).id(); };

    function obterPendenciaPorOriginarioEmAnalise() { return porOriginarioDataTable.row({ selected: true }).id(); };

    function obterPendenciaPorEstornadaEmAnalise() { return porEstornadaDataTable.row({ selected: true }).id(); };

    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        // Recarrega a tabela de requisições.
        requisicoesDataTable.ajax.reload();
    }

    function atualizarRequisicaoEmAnalise() {
        limparRequisicaoEmAnalise();

        let requisicaoEmAnalise = obterRequisicaoEmAnalise();

        if (requisicaoEmAnalise) {
            $('#ViewModel_NumeroDaRequisicao').val(requisicaoEmAnalise);

            $('.barra-navegacao button').prop('disabled', false);

            carregarDadosRequisicao(requisicaoEmAnalise, true, false);
            carregarListasDeComparacao(requisicaoEmAnalise);
        }
    }

    async function carregarListasDeComparacao(requisicao) {
        try {
            limparPendenciaPorCpfEmAnalise();
            limparPendenciaPorOriginarioEmAnalise();
            limparPendenciaPorEstornadaEmAnalise();
            ocorrenciasDataTable.clear().draw();

            // Carrega as listas de requisições e ocorrências.
            abp.ui.block({ elm: 'body', busy: true })

            const results = await Promise.allSettled([
                service.buscarRequisicoesParaComparacaoPorCpf(requisicao),
                service.buscarRequisicoesParaComparacaoPorOriginario(requisicao),
                Promise.reject('Backend não implementado.'),  //service.buscarRequisicoesParaComparacaoPorEstornada(requisicao),
                service.buscaOcorrenciasAnalise(requisicao),
            ]);

            if (!preencherTabela(results[0], porCpfDataTable))
                limparPendenciaPorCpfEmAnalise();

            if (!preencherTabela(results[1], porOriginarioDataTable))
                limparPendenciaPorOriginarioEmAnalise();

            if (!preencherTabela(results[2], porEstornadaDataTable))
                limparPendenciaPorEstornadaEmAnalise();

            if (!preencherTabela(results[3], ocorrenciasDataTable))
                ocorrenciasDataTable.clear().draw();

            if (obterPendenciaPorCpfEmAnalise()) showTable('#ListaPorCPF');
            else if (obterPendenciaPorOriginarioEmAnalise()) showTable('#ListaPorOriginario');
            else if (obterPendenciaPorEstornadaEmAnalise()) showTable('#ListaPorEstornada');
            else showTable('#ListaPorCPF');
        }
        catch (ex) {
            abp.notify.error('Falha para obter dados da requisição.');
        }
        finally {
            abp.ui.unblock();
        }

        function preencherTabela(result, tabela) {
            if (result.status === 'fulfilled' && result.value.totalCount > 0) {
                tabela.clear().rows.add(result.value.items).draw();
                return true;
            }
            return false;
        }
    }

    function atualizarRequisicaoPorCpfEmAnalise() {
        let pendenciaPorCpfEmAnalise = obterPendenciaPorCpfEmAnalise();

        if (pendenciaPorCpfEmAnalise) {
            $('#ViewModel_NumeroRequisicaoComparada').val(pendenciaPorCpfEmAnalise);

            carregarDadosRequisicao(pendenciaPorCpfEmAnalise, false, false);
        }
        else {
            limparPendenciaPorCpfEmAnalise();
        }
    }

    function atualizarRequisicaoPorOriginarioEmAnalise() {
        let pendenciaPorOriginarioEmAnalise = obterPendenciaPorOriginarioEmAnalise();

        if (pendenciaPorOriginarioEmAnalise) {
            $('#ViewModel_NumeroRequisicaoComparada').val(pendenciaPorOriginarioEmAnalise);

            carregarDadosRequisicao(pendenciaPorOriginarioEmAnalise, false, false);
        }
        else {
            limparPendenciaPorOriginarioEmAnalise();
        }
    }

    function atualizarRequisicaoPorEstornadaEmAnalise() {
        let pendenciaPorEstornadaEmAnalise = obterPendenciaPorEstornadaEmAnalise();

        if (pendenciaPorEstornadaEmAnalise) {
            $('#ViewModel_NumeroRequisicaoComparada').val(pendenciaPorEstornadaEmAnalise);

            carregarDadosRequisicao(pendenciaPorEstornadaEmAnalise, false, false);
        }
        else {
            limparPendenciaPorEstornadaEmAnalise();
        }
    }

    function limparRequisicaoEmAnalise() {
        $('#ViewModel_NumeroDaRequisicao').val('');
        $('.barra-navegacao button').prop('disabled', true);

        window.listaExportacaoObservacao = new Map();
        limparDadosRequisicao(true);
        limparPendenciaPorCpfEmAnalise();
        limparPendenciaPorOriginarioEmAnalise();
        limparPendenciaPorEstornadaEmAnalise();
        ocorrenciasDataTable.clear().draw();
    }

    function limparPendenciaPorCpfEmAnalise() {
        $('#ViewModel_NumeroRequisicaoComparada').val('');
        $('.botao-navegacao-tabela[data-tabela="ProtocolosPorCpfTable"]').prop('disabled', true);

        porCpfDataTable.clear().draw();
        limparDadosRequisicao(false);
    }

    function limparPendenciaPorOriginarioEmAnalise() {
        $('#ViewModel_NumeroRequisicaoComparada').val('');
        $('.botao-navegacao-tabela[data-tabela="ProtocolosPorOriginarioTable"]').prop('disabled', true);

        porOriginarioDataTable.clear().draw();
        limparDadosRequisicao(false);
    }

    function limparPendenciaPorEstornadaEmAnalise() {
        $('#ViewModel_NumeroRequisicaoComparada').val('');
        $('.botao-navegacao-tabela[data-tabela="ProtocolosPorEstornadaTable"]').prop('disabled', true);

        porEstornadaDataTable.clear().draw();
        limparDadosRequisicao(false);
    }

    function resetRequisicoesDataTable() {
        requisicoesDataTable.destroy();
        $('#PendenciaRequisicoesTable').empty();

        limparRequisicaoEmAnalise();
        requisicoesDataTable = inicializarRequisicoesDataTable();
    }

    function hideAllTables() {
        Object.values(tables).forEach(containerId => { $(containerId).hide(); });
        $('.botao-navegacao-tabela[data-tabela^="ProtocolosPor"]').prop('disabled', true);
        $('.botao-lista-comparacao').removeClass('selecionado');
    }

    function showTable(buttonId) {
        hideAllTables();
        $(tables[buttonId]).show();
        $(buttonId).addClass('selecionado');

        let tabela;
        switch (buttonId) {
            case '#ListaPorCPF': tabela = porCpfDataTable; break;
            case '#ListaPorOriginario': tabela = porOriginarioDataTable; break;
            case '#ListaPorEstornada': tabela = porEstornadaDataTable; break;
        }
        if (tabela) {
            $('#ViewModel_NumeroRequisicaoComparada').val('');
            limparDadosRequisicao(false);

            // Seleciona a primeira linha.
            selecionarLinhaTabela(tabela, 0);
        }
    }
});
