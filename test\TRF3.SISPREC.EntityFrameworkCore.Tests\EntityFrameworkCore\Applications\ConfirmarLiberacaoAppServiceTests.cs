using NSubstitute;
using Shouldly;
using TRF3.SISPREC.AnalisePendencias;
using TRF3.SISPREC.Analises.Dtos;
using TRF3.SISPREC.ConfirmarLiberacoes;
using TRF3.SISPREC.Enums;

namespace TRF3.SISPREC.EntityFrameworkCore.Applications
{
    public class ConfirmarLiberacaoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
    {
        private readonly IConfirmarLiberacaoRepository _confirmarLiberacaoRepository;
        private readonly ConfirmarLiberacaoAppService _confirmarLiberacaoAppService;

        public ConfirmarLiberacaoAppServiceTests()
        {
            _confirmarLiberacaoRepository = Substitute.For<IConfirmarLiberacaoRepository>();
            _confirmarLiberacaoAppService = new ConfirmarLiberacaoAppService(_confirmarLiberacaoRepository);
        }


        [Fact]
        public async Task GetRequisicoes_Deve_Passar()
        {
            //Arrange
            var getListInput = new AnaliseGetListInput
            {
                TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
                Ano = 2021,
                Mes = 1,
                DataInicio = DateTime.Now,
                DataTermino = DateTime.Now.AddDays(1)
            };

            _confirmarLiberacaoRepository.BuscarRequisicoesPendentesParaLiberacao(
                Arg.Any<ETipoProcedimentoRequisicao>(),
                Arg.Any<int>(),
                Arg.Any<int>(),
                Arg.Any<DateTime>(),
                Arg.Any<DateTime>(),
                Arg.Any<string>()).Returns(new List<RequisicoesPendentes>() { new() { NumeroProtocoloRequisicaoPendente = "1" } });

            //Act
            var result = await _confirmarLiberacaoAppService.GetRequisicoes(getListInput);

            //Assert
            result.ShouldNotBeNull();
            await _confirmarLiberacaoRepository.Received(1).BuscarRequisicoesPendentesParaLiberacao(
                Arg.Any<ETipoProcedimentoRequisicao>(),
                Arg.Any<int>(),
                Arg.Any<int>(),
                Arg.Any<DateTime>(),
                Arg.Any<DateTime>(),
                Arg.Any<string>());
        }

        [Fact]
        public async Task GetRequisicoes_Nao_Deve_Passar()
        {
            //Arrange
            var getListInput = new AnaliseGetListInput
            {
                TipoProcedimento = ETipoProcedimentoRequisicao.PRC,
                Ano = 2021,
                Mes = 1,
                DataInicio = DateTime.Now,
                DataTermino = DateTime.Now.AddDays(1)
            };

            _confirmarLiberacaoRepository.BuscarRequisicoesPendentesParaLiberacao(
                Arg.Any<ETipoProcedimentoRequisicao>(),
                Arg.Any<int>(),
                Arg.Any<int>(),
                Arg.Any<DateTime>(),
                Arg.Any<DateTime>(),
                Arg.Any<string>()).Returns(new List<RequisicoesPendentes>());

            //Act
            var result = await _confirmarLiberacaoAppService.GetRequisicoes(getListInput);

            //Assert
            result.Items.Count.ShouldBe(0);
        }

        [Fact]
        public async Task GetInformacaoBasica_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacaoRepository.BuscaPorInformacaoBasicaRequisicoes(Arg.Any<string>()).Returns(
                new DadosBasicoParaConfirmarLiberacao
                {
                    AnoPropos = 1,
                    MesPropos = 9,
                    TipoProcedimento = ETipoProcedimentoRequisicao.PRC.ToString(),
                    NumeroProtocoloRequisicao = numeroProtocolo,
                    SituacaoProposta = "ATIVO",
                    SituacaoRequisicao = "PAGO TOTAL - Informado ao Juizo"
                });

            //Act
            var result = await _confirmarLiberacaoAppService.GetInformacaoBasica(numeroProtocolo);

            //Assert
            result.ShouldNotBeNull();
            await _confirmarLiberacaoRepository.Received(1).BuscaPorInformacaoBasicaRequisicoes(Arg.Any<string>());
        }

        [Fact]
        public async Task GetInformacaoBasica_Nao_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacaoRepository.BuscaPorInformacaoBasicaRequisicoes(Arg.Any<string>()).Returns((DadosBasicoParaConfirmarLiberacao?)null);

            //Act
            var result = await _confirmarLiberacaoAppService.GetInformacaoBasica(numeroProtocolo);

            //Assert
            result.ShouldBeNull();
        }

        [Fact]
        public async Task GetOcorrencias_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacaoRepository.BuscaPorOcorrenciasRequisicoes(Arg.Any<string>())
                .Returns(new List<DadosOcorrenciasParaConfirmarLiberacao>()
                {
                    new() {
                        DataOCorrencia = DateTime.Now,
                        TipoOcorrencia = "Órgão PSS",
                        Descricao = "Teste "
                    }
                });

            //Act
            var result = await _confirmarLiberacaoAppService.GetOcorrencias(numeroProtocolo);

            //Assert
            result.ShouldNotBeNull();
            await _confirmarLiberacaoRepository.Received(1).BuscaPorOcorrenciasRequisicoes(Arg.Any<string>());
        }

        [Fact]
        public async Task GetOcorrencias_Nao_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacaoRepository.BuscaPorOcorrenciasRequisicoes(Arg.Any<string>()).Returns(new List<DadosOcorrenciasParaConfirmarLiberacao>());

            //Act
            var result = await _confirmarLiberacaoAppService.GetOcorrencias(numeroProtocolo);

            //Assert
            result.Items.Count.ShouldBe(0);
        }

        [Fact]
        public async Task GetJustificastivaAnalises_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacaoRepository.BuscaPorJustificativasAnalises(Arg.Any<string>())
                .Returns(new List<JustificativasAnalises>()
                {
                    new() {
                         DataAnalise = DateTime.Now,
                         ComplementoJustificativa = "Complemento Justificativa",
                         DescricaoJustificativa = "Descrição Justificativa Análise",
                         UsuarioAnalise = "usuarioTeste",
                         TipoAcao = "CANCELAMENTO",
                         DescricaoTela = "Pendências"
                    }
                });

            //Act
            var result = await _confirmarLiberacaoAppService.GetJustificastivaAnalises(numeroProtocolo);

            //Assert
            result.ShouldNotBeNull();
            await _confirmarLiberacaoRepository.Received(1).BuscaPorJustificativasAnalises(Arg.Any<string>());
        }

        [Fact]
        public async Task GetJustificastivaAnalises_Nao_Deve_Passar()
        {
            //Arrange
            var numeroProtocolo = "123456";

            _confirmarLiberacaoRepository.BuscaPorJustificativasAnalises(Arg.Any<string>()).Returns(new List<JustificativasAnalises>());

            //Act
            var result = await _confirmarLiberacaoAppService.GetJustificastivaAnalises(numeroProtocolo);

            //Assert
            result.Items.Count.ShouldBe(0);
        }
    }
}
