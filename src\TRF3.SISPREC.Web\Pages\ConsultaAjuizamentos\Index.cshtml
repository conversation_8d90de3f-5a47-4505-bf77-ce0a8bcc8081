@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.ConsultaAjuizamentos
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Consulta Ajuizamentos";
    PageLayout.Content.MenuItemName = SISPRECMenus.Ajuizamento;
}

@section scripts
{
    <abp-script src="/js/exportacao-excel.js" />
    <abp-script src="/Pages/ConsultaAjuizamentos/index.js" />
    <abp-script src="/js/componente-utils.js" />
}
@section styles
{
    <abp-style src="/Pages/ConsultaAjuizamentos/index.css" />
}

<abp-card>
    <abp-card-body>
        <abp-dynamic-form abp-model="ConsultaFilter" id="ConsultaFilter" required-symbols="false" column-size="_3">
            <abp-collapse-body id="ConsultaFilterCollapse" show="true">
                <abp-form-content />
            </abp-collapse-body>
            <abp-column class="d-flex align-items-start">
                <abp-button size="Small" class="mx-0" button-type="Primary" id="pesquisar">
                    Pesquisar
                </abp-button>
                <abp-button block="true" size="Small" id="exporta-excel" class="btn-primary-outline custom-border mx-1">
                    Exportar Excel
                </abp-button>
            </abp-column>
            <hr />
        </abp-dynamic-form>
        <abp-table striped-rows="true" id="ConsultaAjuizamentoTable" class="nowrap" />
        <abp-row class="mt-3">
             <abp-column class="text-start">
                <abp-button id="marcar-todos" text="Marcar/Desmarcar Todos" button-type="Outline_Primary" size="Medium" />
               </abp-column>
             <abp-column class="text-end">
                <abp-button id="btnCadastrarJustificativa" text="Cadastrar Justificativa" data-tipo-analise="Verificação CPF/CNPJ" button-type="Outline_Primary" size="Medium" />
            </abp-column>
        </abp-row>
    </abp-card-body>
</abp-card>
