using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRF3.SISPREC.ModelosDocumentos;
using TRF3.SISPREC.EntityFrameworkCore.Consts;

namespace TRF3.SISPREC.EntityFrameworkCore.Configuration
{
    public class ModeloDocumentoConfiguration : IEntityTypeConfiguration<ModeloDocumento>
    {
        private const string modeloDocume = "_MODELO_DOCUME";
        public const string NomeTabela = Constants.PrefixTable.ANA + modeloDocume + "NTO";

        public void Configure(EntityTypeBuilder<ModeloDocumento> builder)
        {

            builder.ToTable(NomeTabela);

            builder.HasKey(x => x.ModeloDocumentoId)
                .HasName($"{NomeTabela}_P01");

            builder.Property(x => x.ModeloDocumentoId)
                .HasColumnName($"{Constants.PrefixColumn.SEQ + modeloDocume}")
                .IsRequired();

            builder.Property(x => x.SetorId)
                .HasColumnName("SEQ_SETOR")
                .IsRequired();

            builder.Property(x => x.NomeModelo)
                .HasColumnType("varchar")
                .HasMaxLength(255)
                .IsRequired()
                .HasColumnName($"{Constants.PrefixColumn.NOM + modeloDocume}");

            builder.Property(x => x.TextoDocumento)
                .HasColumnName($"{Constants.PrefixColumn.TXT + modeloDocume}")
                .IsRequired();

            builder.Property(x => x.IsDeleted)
                .HasColumnName($"{Constants.PrefixColumn.SIN}_EXCLUI")
                .HasDefaultValue(false)
                .IsRequired();

            builder
                .HasOne(x => x.Setor)
                    .WithMany(x => x.ModelosDocumentos)
                        .HasForeignKey(x => x.SetorId)
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName($"{NomeTabela}_R01");

        }
    }
}