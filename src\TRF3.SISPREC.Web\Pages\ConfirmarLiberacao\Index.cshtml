@page
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model TRF3.SISPREC.Web.Pages.ConfirmarLiberacao.IndexModel
@{
    PageLayout.Content.Title = "Confirmar Liberação";

    ViewData["Meses"] = Model.MesesComItemVazio;
}

@section scripts
{
    <abp-script src="/Pages/ConfirmarLiberacao/index.js" />
    <abp-script src="/js/componente-utils.js" />
}

@section styles
{
    <abp-style src="/css/app/analises.css" />
}

<abp-card>
    <abp-card-body>

        <!--BOTÃO RECOLHER/EXPANDIR-->
        <div class="tooltip-container float-end" style="margin:-1.5rem">
            <abp-button class="ocultar-secao-botao" id="btnOcultarSecaoTopo" size="Small" button-type="Default" abp-collapse-id="ocultarSecaoTopo">ᐱ</abp-button>
            <span class="tooltiptext" id="tooltipText">Recolher</span>
        </div>

        <!--SEÇÃO OCULTAR E EXIBIR-->
        <abp-collapse-body id="ocultarSecaoTopo" show="true">
            <form asp-for="ConfirmarLiberacaoFilterInput" id="ConfirmarLiberacaoFilterInput">
                <partial name="Shared/Partials/_AnaliseFiltroForm" model="@Model.ConfirmarLiberacaoFilterInput" />
            </form>

            <!--LISTAS-->
            <abp-row class="lista-dados">
                <abp-column style="max-width: 20%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="LiberacaoRequisicoesTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
                <abp-column style="max-width: 80%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="OcorrenciaTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
            </abp-row>
        </abp-collapse-body>

        <!--BOTÕES-->
        <abp-row class="d-flex button-container barra-navegacao">
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="LiberacaoRequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="width: 135px; margin: -6px;">
                <abp-input asp-for="ViewModel!.NumeroDaRequisicao" readonly="true" />
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="LiberacaoRequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" size="_1">
                <abp-input asp-for="ViewModel!.TipoProcedimento" readonly="true"></abp-input>
            </abp-column>
            <abp-column class="col-auto" size="_1">
                <abp-input asp-for="ViewModel!.Ano" readonly="true"></abp-input>
            </abp-column>
            <abp-column class="col-auto" size="_1">
                <abp-input asp-for="ViewModel!.Mes" readonly="true"></abp-input>
            </abp-column>
            <abp-column class="col-auto" size="_2">
                <abp-input asp-for="ViewModel!.SituacaoRequisicao" readonly="true"></abp-input>
            </abp-column>
            <abp-column class="col-auto" size="_2">
                <abp-input asp-for="ViewModel!.SituacaoProposta" readonly="true"></abp-input>
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Liberar" id="btnLiberar"></abp-button>
            </abp-column>
        </abp-row>

        <abp-row>
            <abp-column>
                <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="DadosAnalise" class="nowrap" />
            </abp-column>
        </abp-row>
    </abp-card-body>
</abp-card>

