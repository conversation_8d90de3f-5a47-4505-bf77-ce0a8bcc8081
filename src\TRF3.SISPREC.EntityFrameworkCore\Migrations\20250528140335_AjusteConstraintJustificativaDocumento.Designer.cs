﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TRF3.SISPREC.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

#nullable disable

namespace TRF3.SISPREC.Migrations
{
    [DbContext(typeof(SISPRECDbContext))]
    [Migration("20250528140335_AjusteConstraintJustificativaDocumento")]
    partial class AjusteConstraintJustificativaDocumento
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.SqlServer)
                .HasAnnotation("ProductVersion", "8.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("TRF3.SISPREC.AcaoTipos.AcaoTipo", b =>
                {
                    b.Property<int>("AcaoTipoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ACAO_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AcaoTipoId"));

                    b.Property<bool>("Ativo")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_ATIVO");

                    b.Property<char>("Codigo")
                        .HasMaxLength(1)
                        .HasColumnType("nvarchar(1)")
                        .HasColumnName("COD_ACAO_TIPO");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("DES_ACAO_TIPO");

                    b.HasKey("AcaoTipoId")
                        .HasName("ANA_ACAO_TIPO_P01");

                    b.ToTable("ANA_ACAO_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.AcoesJustificativa.AcaoJustificativa", b =>
                {
                    b.Property<int>("AcaoJustificativaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ACAO_JUSTIF");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AcaoJustificativaId"));

                    b.Property<int>("AcaoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ACAO_TIPO");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(500)")
                        .HasColumnName("DES_ACAO_JUSTIF");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("Usuario")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("NOM_USUARI");

                    b.HasKey("AcaoJustificativaId")
                        .HasName("ANA_ACAO_JUSTIFICATIVA_P01");

                    b.HasIndex("Descricao")
                        .IsUnique()
                        .HasDatabaseName("ANA_ACAO_JUSTIFICATIVA_i01");

                    b.ToTable("ANA_ACAO_JUSTIFICATIVA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.AcoesOriginarias.AcaoOriginaria", b =>
                {
                    b.Property<long>("Seq_Acao_Origi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_ACAO_ORIGI");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Seq_Acao_Origi"));

                    b.Property<DateTime?>("DataAjuizamento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_AJUIZA");

                    b.Property<DateTime?>("DataRegistro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_CADAST");

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasColumnName("NUM_PROCES");

                    b.Property<int>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Acao_Origi")
                        .HasName("CJF_PROCESSO_ACAO_ORIGINARIA_P01");

                    b.ToTable("CJF_PROCESSO_ACAO_ORIGINARIA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.AcoesOriginariasUnidadesJudiciais.AcaoOriginariaUnidadeJudicial", b =>
                {
                    b.Property<long?>("AcaoOriginariaId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_ACAO_ORIGI");

                    b.Property<int?>("UnidadeJudicialId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI");

                    b.Property<int?>("TipoUnidadeJudicialId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI_TIPO");

                    b.Property<int?>("UnidadeJudicialTipoNaturezaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI_TIPO_NATURE");

                    b.HasKey("AcaoOriginariaId", "UnidadeJudicialId", "TipoUnidadeJudicialId", "UnidadeJudicialTipoNaturezaId")
                        .HasName("CJF_ACAO_ORIGINARIA_UNIDADE_JUDICIAL_P01");

                    b.ToTable("CJF_ACAO_ORIGINARIA_UNIDADE_JUDICIAL", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.AdvogadosJudiciais.AdvogadoJudicial", b =>
                {
                    b.Property<int>("AdvogadoJudicialId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("COD_ADVOGA");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AdvogadoJudicialId"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("CodigoOab")
                        .HasMaxLength(9)
                        .HasColumnType("nchar(9)")
                        .HasColumnName("COD_OAB")
                        .IsFixedLength();

                    b.Property<string>("Cpf")
                        .HasMaxLength(11)
                        .HasColumnType("nchar(11)")
                        .HasColumnName("NUM_CPF_ADVOGA")
                        .IsFixedLength();

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)")
                        .HasColumnName("NOM_ADVOGA");

                    b.Property<string>("NomeSocial")
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)")
                        .HasColumnName("NOM_SOCIAL");

                    b.HasKey("AdvogadoJudicialId")
                        .HasName("REQ_ADVOGADO_JUDICIAL_P01");

                    b.HasIndex("CodigoOab")
                        .HasDatabaseName("REQ_ADVOGADO_JUDICIAL_i01");

                    b.ToTable("REQ_ADVOGADO_JUDICIAL", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Agencias.Agencia", b =>
                {
                    b.Property<int>("AgenciaId")
                        .HasColumnType("int")
                        .HasColumnName("NUM_AGENCI");

                    b.Property<int>("BancoId")
                        .HasColumnType("int")
                        .HasColumnName("NUM_BANCO");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("CodDigitoVerifi")
                        .IsRequired()
                        .HasColumnType("char(1)")
                        .HasColumnName("COD_DIGITO_VERIFI");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<int>("MunicipioId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MUNICI");

                    b.Property<string>("NomeAgencia")
                        .IsRequired()
                        .HasColumnType("varchar(25)")
                        .HasColumnName("NOM_AGENCI");

                    b.HasKey("AgenciaId", "BancoId")
                        .HasName("TRF_AGENCIA_P01");

                    b.ToTable("TRF_AGENCIA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.AnaliseTelas.AnaliseTela", b =>
                {
                    b.Property<int>("AnaliseTelaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ANALIS_TELA");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AnaliseTelaId"));

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("VARCHAR(200)")
                        .HasColumnName("DES_ANALIS_TELA");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.HasKey("AnaliseTelaId")
                        .HasName("ANA_ANALISE_TELA_P01");

                    b.ToTable("ANA_ANALISE_TELA", (string)null);

                    b.HasData(
                        new
                        {
                            AnaliseTelaId = 1,
                            Descricao = "CPF/CNPJ",
                            IsDeleted = false
                        },
                        new
                        {
                            AnaliseTelaId = 2,
                            Descricao = "Pendências",
                            IsDeleted = false
                        },
                        new
                        {
                            AnaliseTelaId = 3,
                            Descricao = "Prevenção",
                            IsDeleted = false
                        },
                        new
                        {
                            AnaliseTelaId = 4,
                            Descricao = "Reinclusão",
                            IsDeleted = false
                        },
                        new
                        {
                            AnaliseTelaId = 5,
                            Descricao = "Campo Observação",
                            IsDeleted = false
                        },
                        new
                        {
                            AnaliseTelaId = 6,
                            Descricao = "Órgão PSS",
                            IsDeleted = false
                        },
                        new
                        {
                            AnaliseTelaId = 7,
                            Descricao = "Nomes de Partes",
                            IsDeleted = false
                        });
                });

            modelBuilder.Entity("TRF3.SISPREC.AntecessoresBeneficiarios.AntecessorBeneficiario", b =>
                {
                    b.Property<int>("Seq_Benefi_Antece")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_BENEFI_ANTECE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Benefi_Antece"));

                    b.Property<long>("BeneficiarioId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_BENEFI");

                    b.Property<string>("CpfCnpjAntecessor")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasColumnName("COD_CPFCPJ_BENEFI_ANTECE");

                    b.Property<DateTime?>("DataRegistro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_REGIST");

                    b.Property<DateTime?>("DataSucessao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_SUCESS");

                    b.Property<string>("NomeAntecessor")
                        .IsRequired()
                        .HasColumnType("varchar(100)")
                        .HasColumnName("DES_NOME_BENEFI_ANTECE");

                    b.Property<bool>("Originario")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_ORIGIN");

                    b.HasKey("Seq_Benefi_Antece")
                        .HasName("CJF_BENEFICIARIO_ANTECESSORES_P01");

                    b.ToTable("CJF_BENEFICIARIO_ANTECESSORES", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Assuntos.Assunto", b =>
                {
                    b.Property<int>("Seq_Assunt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ASSUNT");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Assunt"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("CodigoCJF")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_CJF");

                    b.Property<string>("CodigoCNJ")
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_CNJ");

                    b.Property<DateTime?>("DataAtualizacao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_ATUALI");

                    b.Property<DateTime?>("DataCadastro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_INCLU");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("DescricaoCJF")
                        .HasColumnType("varchar(300)")
                        .HasColumnName("NOM_CJF");

                    b.Property<string>("DescricaoCNJ")
                        .HasColumnType("varchar(300)")
                        .HasColumnName("NOM_CNJ");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Assunt")
                        .HasName("CJF_Assunto_P01");

                    b.HasIndex("CodigoCJF")
                        .HasDatabaseName("CJF_ASSUNTO_i01");

                    b.ToTable("CJF_ASSUNTO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Assuntos.AssuntoAuxiliar", b =>
                {
                    b.Property<int>("AssuntoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ASSUNT");

                    b.Property<bool?>("IndicadorDesapropriacao")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_DESAPR");

                    b.Property<bool?>("IndicadorExecucaoFiscal")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_EXECUC_FISCAL");

                    b.Property<bool?>("IndicadorObrigacaoPSS")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_OBRIGA_PSS");

                    b.Property<bool?>("IndicadorRRA")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_RRA");

                    b.Property<bool?>("IndicadorTributario")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_TRIBUT");

                    b.HasKey("AssuntoId")
                        .HasName("TRF_ASSUNTO_P01");

                    b.ToTable("TRF_ASSUNTO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Assuntos.AssuntoDespesa", b =>
                {
                    b.Property<int?>("AssuntoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ASSUNT");

                    b.Property<int?>("DespesaClassificacaoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CLASSI_DESPES");

                    b.HasKey("AssuntoId", "DespesaClassificacaoId")
                        .HasName("TRF_ASSUNTO_DESPESA_P01");

                    b.ToTable("TRF_ASSUNTO_DESPESA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Bancos.Banco", b =>
                {
                    b.Property<int>("BancoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("NUM_BANCO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BancoId"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("NomeBanco")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("NOM_BANCO");

                    b.HasKey("BancoId")
                        .HasName("TRF_BANCO_P01");

                    b.ToTable("TRF_BANCO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiarioIdentificacaoTipos.BeneficiarioIdentificacaoTipo", b =>
                {
                    b.Property<int>("Seq_Benefi_Identi_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_BENEFI_IDENTI_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Benefi_Identi_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("COD_BENEFI_TIPO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_BENEFI_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Benefi_Identi_Tipo")
                        .HasName("CJF_BENEFI_IDENTI_TIPO_P01");

                    b.ToTable("CJF_BENEFI_IDENTI_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiarioSucessaoTipos.BeneficiarioSucessaoTipo", b =>
                {
                    b.Property<int>("Seq_Sucess_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SUCESS_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Sucess_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_SUCESS_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.HasKey("Seq_Sucess_Tipo")
                        .HasName("CJF_SUCESSAO_TIPO_P01");

                    b.ToTable("CJF_SUCESSAO_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiarioTipos.BeneficiarioTipo", b =>
                {
                    b.Property<int>("Seq_Benefi_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_BENEFI_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Benefi_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_BENEFI_TIPO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_BENEFI_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Benefi_Tipo")
                        .HasName("CJF_BENEFI_TIPO_P01");

                    b.ToTable("CJF_BENEFI_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Beneficiarios.Beneficiario", b =>
                {
                    b.Property<long>("Seq_Benefi")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_BENEFI");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Seq_Benefi"));

                    b.Property<bool?>("AdvogadoNaCausa")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_ADVOG_NA_CAUSA");

                    b.Property<int>("CompetenciaExpedicaoAno")
                        .HasColumnType("int")
                        .HasColumnName("NUM_COMPT_EXPED_ANO");

                    b.Property<int?>("CondicaoServidorTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SERVID_CONDIC_TIPO");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_CANCEL");

                    b.Property<DateTime?>("DataCompensacao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_COMPEN");

                    b.Property<DateTime?>("DataIntimacao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_INTIMA");

                    b.Property<DateTime?>("DataNascimento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_NASCI");

                    b.Property<DateTime?>("DataRegistro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_REGIST");

                    b.Property<int>("ExercicioAnteriorNumeroMeses")
                        .HasColumnType("int")
                        .HasColumnName("NUM_EXERC_ANTER_NUM_MESES");

                    b.Property<int>("ExercicioCorrenteNumeroMeses")
                        .HasColumnType("int")
                        .HasColumnName("NUM_EXERC_CORRE_NUM_MESES");

                    b.Property<int>("IdentificacaoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_BENEFI_IDENTI_TIPO");

                    b.Property<int>("IsentoIRRF")
                        .HasColumnType("int")
                        .HasColumnName("IDE_ISENTO_IRRF");

                    b.Property<long>("ProcessoId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PROCES");

                    b.Property<long>("SEQ_CONTA_BANCAR")
                        .HasColumnType("bigint");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.Property<int?>("SucessaoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SUCESS_TIPO");

                    b.Property<int>("TipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_BENEFI_TIPO");

                    b.Property<int>("TipoMovimentoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MOVIME_TIPO");

                    b.Property<int?>("UnidadeId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD");

                    b.HasKey("Seq_Benefi")
                        .HasName("CJF_BENEFICIARIOS_P01");

                    b.ToTable("CJF_BENEFICIARIOS", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiariosOrigensPCT.BeneficiarioOrigemPCT", b =>
                {
                    b.Property<int>("ControleProcessamentoId")
                        .HasColumnType("int")
                        .HasColumnName("CONTROLEPROCESSAMENTOID");

                    b.Property<string>("NumeroProcesso")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUMEROPROCESSO");

                    b.Property<string>("CodigoBeneficiario")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("CODIGOBENEFICIARIO");

                    b.Property<bool?>("AdvogadoCausa")
                        .HasColumnType("bit")
                        .HasColumnName("ADVOGADOCAUSA");

                    b.Property<int>("AnoProposta")
                        .HasColumnType("int")
                        .HasColumnName("ANOPROPOSTA");

                    b.Property<int?>("CodigoAgencia")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOAGENCIA");

                    b.Property<int?>("CodigoBanco")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOBANCO");

                    b.Property<string>("CodigoBeneficiarioAntecessor")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGOBENEFICIARIOANTECESSOR");

                    b.Property<string>("CodigoReceita1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA1");

                    b.Property<string>("CodigoReceita10")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA10");

                    b.Property<string>("CodigoReceita11")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA11");

                    b.Property<string>("CodigoReceita12")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA12");

                    b.Property<string>("CodigoReceita13")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA13");

                    b.Property<string>("CodigoReceita14")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA14");

                    b.Property<string>("CodigoReceita15")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA15");

                    b.Property<string>("CodigoReceita2")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA2");

                    b.Property<string>("CodigoReceita3")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA3");

                    b.Property<string>("CodigoReceita4")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA4");

                    b.Property<string>("CodigoReceita5")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA5");

                    b.Property<string>("CodigoReceita6")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA6");

                    b.Property<string>("CodigoReceita7")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA7");

                    b.Property<string>("CodigoReceita8")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA8");

                    b.Property<string>("CodigoReceita9")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGORECEITA9");

                    b.Property<int?>("CodigoTipoDocumento1")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO1");

                    b.Property<int?>("CodigoTipoDocumento10")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO10");

                    b.Property<int?>("CodigoTipoDocumento11")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO11");

                    b.Property<int?>("CodigoTipoDocumento12")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO12");

                    b.Property<int?>("CodigoTipoDocumento13")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO13");

                    b.Property<int?>("CodigoTipoDocumento14")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO14");

                    b.Property<int?>("CodigoTipoDocumento15")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO15");

                    b.Property<int?>("CodigoTipoDocumento2")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO2");

                    b.Property<int?>("CodigoTipoDocumento3")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO3");

                    b.Property<int?>("CodigoTipoDocumento4")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO4");

                    b.Property<int?>("CodigoTipoDocumento5")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO5");

                    b.Property<int?>("CodigoTipoDocumento6")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO6");

                    b.Property<int?>("CodigoTipoDocumento7")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO7");

                    b.Property<int?>("CodigoTipoDocumento8")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO8");

                    b.Property<int?>("CodigoTipoDocumento9")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOTIPODOCUMENTO9");

                    b.Property<int>("CodigoUORCadastradora")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOUORCADASTRADORA");

                    b.Property<string>("CondicaoServidor")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CONDICAOSERVIDOR");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAALTERACAO");

                    b.Property<DateTime?>("DataCompensacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATACOMPENSACAO");

                    b.Property<DateTime>("DataExtracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAEXTRACAO");

                    b.Property<DateTime?>("DataImportacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAIMPORTACAO");

                    b.Property<DateTime?>("DataInclusao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAINCLUSAO");

                    b.Property<DateTime?>("DataIntimacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAINTIMACAO");

                    b.Property<DateTime?>("DataNascimento")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATANASCIMENTO");

                    b.Property<DateTime?>("DataSucessao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATASUCESSAO");

                    b.Property<string>("IdentificacaoDebito1")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO1");

                    b.Property<string>("IdentificacaoDebito10")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO10");

                    b.Property<string>("IdentificacaoDebito11")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO11");

                    b.Property<string>("IdentificacaoDebito12")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO12");

                    b.Property<string>("IdentificacaoDebito13")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO13");

                    b.Property<string>("IdentificacaoDebito14")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO14");

                    b.Property<string>("IdentificacaoDebito15")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO15");

                    b.Property<string>("IdentificacaoDebito2")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO2");

                    b.Property<string>("IdentificacaoDebito3")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO3");

                    b.Property<string>("IdentificacaoDebito4")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO4");

                    b.Property<string>("IdentificacaoDebito5")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO5");

                    b.Property<string>("IdentificacaoDebito6")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO6");

                    b.Property<string>("IdentificacaoDebito7")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO7");

                    b.Property<string>("IdentificacaoDebito8")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO8");

                    b.Property<string>("IdentificacaoDebito9")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICACAODEBITO9");

                    b.Property<string>("IdentificadorPV")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICADORPV");

                    b.Property<string>("IndicadorIsencaoIRRF")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORISENCAOIRRF");

                    b.Property<string>("IndicadorOrdemPagamento107AParcela01")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORORDEMPAGAMENTO107APARCELA01");

                    b.Property<string>("IndicadorOrdemPagamento107AParcela02")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORORDEMPAGAMENTO107APARCELA02");

                    b.Property<string>("IndicadorOrdemPagamento107AParcela03")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORORDEMPAGAMENTO107APARCELA03");

                    b.Property<string>("IndicadorPortadorDeficiencia")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORPORTADORDEFICIENCIA");

                    b.Property<string>("NITCEIPISPASEPCPFCNPJ")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NITCEIPISPASEPCPFCNPJ");

                    b.Property<string>("NomeBeneficiario")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEBENEFICIARIO");

                    b.Property<string>("NomeBeneficiarioAntecessor")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEBENEFICIARIOANTECESSOR");

                    b.Property<string>("NomeSocialBeneficiario")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMESOCIALBENEFICIARIO");

                    b.Property<string>("NumeroConta")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROCONTA");

                    b.Property<int?>("NumeroMesesExerciciosAnteriores")
                        .HasColumnType("int")
                        .HasColumnName("NUMEROMESESEXERCICIOSANTERIORES");

                    b.Property<string>("PreferenciaPortadorDoencaGrave")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PREFERENCIAPORTADORDOENCAGRAVE");

                    b.Property<string>("SucessaoTipo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SUCESSAOTIPO");

                    b.Property<string>("TipoBeneficiario")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOBENEFICIARIO");

                    b.Property<string>("TipoMovimento")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOMOVIMENTO");

                    b.Property<int?>("UOLotacao")
                        .HasColumnType("int")
                        .HasColumnName("UOLOTACAO");

                    b.Property<string>("UsuarioAlteracao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("USUARIOALTERACAO");

                    b.Property<string>("UsuarioInclusao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("USUARIOINCLUSAO");

                    b.Property<decimal?>("ValorCompensacao1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO1");

                    b.Property<decimal?>("ValorCompensacao10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO10");

                    b.Property<decimal?>("ValorCompensacao11")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO11");

                    b.Property<decimal?>("ValorCompensacao12")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO12");

                    b.Property<decimal?>("ValorCompensacao13")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO13");

                    b.Property<decimal?>("ValorCompensacao14")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO14");

                    b.Property<decimal?>("ValorCompensacao15")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO15");

                    b.Property<decimal?>("ValorCompensacao2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO2");

                    b.Property<decimal?>("ValorCompensacao3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO3");

                    b.Property<decimal?>("ValorCompensacao4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO4");

                    b.Property<decimal?>("ValorCompensacao5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO5");

                    b.Property<decimal?>("ValorCompensacao6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO6");

                    b.Property<decimal?>("ValorCompensacao7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO7");

                    b.Property<decimal?>("ValorCompensacao8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO8");

                    b.Property<decimal?>("ValorCompensacao9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMPENSACAO9");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA1");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela10")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA10");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA2");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA3");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA4");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA5");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA6");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA7");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA8");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA9");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA1");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA10");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA2");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA3");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA4");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA5");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA6");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA7");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA8");

                    b.Property<decimal?>("ValorCompensacaoPSSPatronalParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PATRONAL_PARCELA9");

                    b.Property<decimal?>("ValorDeducoesIndividuais")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("DEDUCOES_INDIVIDUAIS");

                    b.Property<decimal?>("ValorExerciciosAnteriores")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_EXERCICIOS_ANTERIORES");

                    b.Property<decimal>("ValorIndividual")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_INDIVIDUAL");

                    b.Property<decimal?>("ValorIndividualExpedicao")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_INDIVIDUAL_EXPEDICAO");

                    b.Property<decimal?>("ValorIndividualUltimoCalculo")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_INDIVIDUAL_ULTIMO_CALCULO");

                    b.Property<decimal?>("ValorJurosSelic1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_1");

                    b.Property<decimal?>("ValorJurosSelic10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_10");

                    b.Property<decimal?>("ValorJurosSelic2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_2");

                    b.Property<decimal?>("ValorJurosSelic3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_3");

                    b.Property<decimal?>("ValorJurosSelic4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_4");

                    b.Property<decimal?>("ValorJurosSelic5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_5");

                    b.Property<decimal?>("ValorJurosSelic6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_6");

                    b.Property<decimal?>("ValorJurosSelic7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_7");

                    b.Property<decimal?>("ValorJurosSelic8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_8");

                    b.Property<decimal?>("ValorJurosSelic9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_9");

                    b.Property<decimal?>("ValorPSSParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA1");

                    b.Property<decimal?>("ValorPSSParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA10");

                    b.Property<decimal?>("ValorPSSParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA2");

                    b.Property<decimal?>("ValorPSSParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA3");

                    b.Property<decimal?>("ValorPSSParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA4");

                    b.Property<decimal?>("ValorPSSParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA5");

                    b.Property<decimal?>("ValorPSSParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA6");

                    b.Property<decimal?>("ValorPSSParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA7");

                    b.Property<decimal?>("ValorPSSParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA8");

                    b.Property<decimal?>("ValorPSSParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA9");

                    b.Property<decimal?>("ValorPSSPatronalParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA1");

                    b.Property<decimal?>("ValorPSSPatronalParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA10");

                    b.Property<decimal?>("ValorPSSPatronalParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA2");

                    b.Property<decimal?>("ValorPSSPatronalParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA3");

                    b.Property<decimal?>("ValorPSSPatronalParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA4");

                    b.Property<decimal?>("ValorPSSPatronalParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA5");

                    b.Property<decimal?>("ValorPSSPatronalParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA6");

                    b.Property<decimal?>("ValorPSSPatronalParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA7");

                    b.Property<decimal?>("ValorPSSPatronalParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA8");

                    b.Property<decimal?>("ValorPSSPatronalParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PATRONAL_PARCELA9");

                    b.Property<decimal?>("ValorParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA1");

                    b.Property<decimal?>("ValorParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA10");

                    b.Property<decimal?>("ValorParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA2");

                    b.Property<decimal?>("ValorParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA3");

                    b.Property<decimal?>("ValorParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA4");

                    b.Property<decimal?>("ValorParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA5");

                    b.Property<decimal?>("ValorParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA6");

                    b.Property<decimal?>("ValorParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA7");

                    b.Property<decimal?>("ValorParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA8");

                    b.Property<decimal?>("ValorParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA9");

                    b.Property<decimal?>("ValorPrincipalTRF1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_1");

                    b.Property<decimal?>("ValorPrincipalTRF10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_10");

                    b.Property<decimal?>("ValorPrincipalTRF2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_2");

                    b.Property<decimal?>("ValorPrincipalTRF3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_3");

                    b.Property<decimal?>("ValorPrincipalTRF4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_4");

                    b.Property<decimal?>("ValorPrincipalTRF5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_5");

                    b.Property<decimal?>("ValorPrincipalTRF6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_6");

                    b.Property<decimal?>("ValorPrincipalTRF7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_7");

                    b.Property<decimal?>("ValorPrincipalTRF8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_8");

                    b.Property<decimal?>("ValorPrincipalTRF9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_9");

                    b.HasKey("ControleProcessamentoId", "NumeroProcesso", "CodigoBeneficiario");

                    b.ToTable("PCT_BENEFICIARIOS", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("CodigoBeneficiario")
                                .HasColumnName("CODIGO_BENEFICIARIO");

                            t.Property("AdvogadoCausa")
                                .HasColumnName("ADVOGADO_CAUSA");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CodigoAgencia")
                                .HasColumnName("CODIGO_AGENCIA");

                            t.Property("CodigoBanco")
                                .HasColumnName("CODIGO_BANCO");

                            t.Property("CodigoReceita1")
                                .HasColumnName("CODIGO_RECEITA1");

                            t.Property("CodigoReceita10")
                                .HasColumnName("CODIGO_RECEITA10");

                            t.Property("CodigoReceita11")
                                .HasColumnName("CODIGO_RECEITA11");

                            t.Property("CodigoReceita12")
                                .HasColumnName("CODIGO_RECEITA12");

                            t.Property("CodigoReceita13")
                                .HasColumnName("CODIGO_RECEITA13");

                            t.Property("CodigoReceita14")
                                .HasColumnName("CODIGO_RECEITA14");

                            t.Property("CodigoReceita15")
                                .HasColumnName("CODIGO_RECEITA15");

                            t.Property("CodigoReceita2")
                                .HasColumnName("CODIGO_RECEITA2");

                            t.Property("CodigoReceita3")
                                .HasColumnName("CODIGO_RECEITA3");

                            t.Property("CodigoReceita4")
                                .HasColumnName("CODIGO_RECEITA4");

                            t.Property("CodigoReceita5")
                                .HasColumnName("CODIGO_RECEITA5");

                            t.Property("CodigoReceita6")
                                .HasColumnName("CODIGO_RECEITA6");

                            t.Property("CodigoReceita7")
                                .HasColumnName("CODIGO_RECEITA7");

                            t.Property("CodigoReceita8")
                                .HasColumnName("CODIGO_RECEITA8");

                            t.Property("CodigoReceita9")
                                .HasColumnName("CODIGO_RECEITA9");

                            t.Property("CodigoTipoDocumento1")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO1");

                            t.Property("CodigoTipoDocumento10")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO10");

                            t.Property("CodigoTipoDocumento11")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO11");

                            t.Property("CodigoTipoDocumento12")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO12");

                            t.Property("CodigoTipoDocumento13")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO13");

                            t.Property("CodigoTipoDocumento14")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO14");

                            t.Property("CodigoTipoDocumento15")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO15");

                            t.Property("CodigoTipoDocumento2")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO2");

                            t.Property("CodigoTipoDocumento3")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO3");

                            t.Property("CodigoTipoDocumento4")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO4");

                            t.Property("CodigoTipoDocumento5")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO5");

                            t.Property("CodigoTipoDocumento6")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO6");

                            t.Property("CodigoTipoDocumento7")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO7");

                            t.Property("CodigoTipoDocumento8")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO8");

                            t.Property("CodigoTipoDocumento9")
                                .HasColumnName("CODIGO_TIPO_DOCUMENTO9");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("CondicaoServidor")
                                .HasColumnName("CONDICAO_SERVIDOR");

                            t.Property("DataAlteracao")
                                .HasColumnName("DATA_ALTERACAO");

                            t.Property("DataCompensacao")
                                .HasColumnName("DATA_COMPENSACAO");

                            t.Property("DataExtracao")
                                .HasColumnName("DAT_EXTRAC");

                            t.Property("DataImportacao")
                                .HasColumnName("DAT_IMPORT");

                            t.Property("DataInclusao")
                                .HasColumnName("DATA_INCLUSAO");

                            t.Property("DataIntimacao")
                                .HasColumnName("DATA_INTIMACAO");

                            t.Property("DataNascimento")
                                .HasColumnName("DATA_NASCIMENTO");

                            t.Property("IdentificacaoDebito1")
                                .HasColumnName("IDENTIFICACAO_DEBITO1");

                            t.Property("IdentificacaoDebito10")
                                .HasColumnName("IDENTIFICACAO_DEBITO10");

                            t.Property("IdentificacaoDebito11")
                                .HasColumnName("IDENTIFICACAO_DEBITO11");

                            t.Property("IdentificacaoDebito12")
                                .HasColumnName("IDENTIFICACAO_DEBITO12");

                            t.Property("IdentificacaoDebito13")
                                .HasColumnName("IDENTIFICACAO_DEBITO13");

                            t.Property("IdentificacaoDebito14")
                                .HasColumnName("IDENTIFICACAO_DEBITO14");

                            t.Property("IdentificacaoDebito15")
                                .HasColumnName("IDENTIFICACAO_DEBITO15");

                            t.Property("IdentificacaoDebito2")
                                .HasColumnName("IDENTIFICACAO_DEBITO2");

                            t.Property("IdentificacaoDebito3")
                                .HasColumnName("IDENTIFICACAO_DEBITO3");

                            t.Property("IdentificacaoDebito4")
                                .HasColumnName("IDENTIFICACAO_DEBITO4");

                            t.Property("IdentificacaoDebito5")
                                .HasColumnName("IDENTIFICACAO_DEBITO5");

                            t.Property("IdentificacaoDebito6")
                                .HasColumnName("IDENTIFICACAO_DEBITO6");

                            t.Property("IdentificacaoDebito7")
                                .HasColumnName("IDENTIFICACAO_DEBITO7");

                            t.Property("IdentificacaoDebito8")
                                .HasColumnName("IDENTIFICACAO_DEBITO8");

                            t.Property("IdentificacaoDebito9")
                                .HasColumnName("IDENTIFICACAO_DEBITO9");

                            t.Property("IdentificadorPV")
                                .HasColumnName("IDENTIFICACAO_PV");

                            t.Property("NITCEIPISPASEPCPFCNPJ")
                                .HasColumnName("NIT_CEI_PIS_PASEP_CPF_CNPJ");

                            t.Property("NomeBeneficiario")
                                .HasColumnName("NOME_BENEFICIARIO");

                            t.Property("NumeroConta")
                                .HasColumnName("NUMERO_CONTA");

                            t.Property("NumeroMesesExerciciosAnteriores")
                                .HasColumnName("NM_EXERCICIOS_ANTERIORES");

                            t.Property("PreferenciaPortadorDoencaGrave")
                                .HasColumnName("PREFERENCIA_PORTADOR_DOENCA_GRAVE");

                            t.Property("TipoBeneficiario")
                                .HasColumnName("TIPO_BENEFICIARIO");

                            t.Property("TipoMovimento")
                                .HasColumnName("TIPO_MOVIMENTO");

                            t.Property("UOLotacao")
                                .HasColumnName("UO_LOTACAO");

                            t.Property("UsuarioAlteracao")
                                .HasColumnName("USUARIO_ALTERACAO");

                            t.Property("UsuarioInclusao")
                                .HasColumnName("USUARIO_INCLUSAO");
                        });

                    b.SplitToTable("PCT_BENEFICIARIOS2", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("CodigoBeneficiario")
                                .HasColumnName("CODIGO_BENEFICIARIO");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CodigoBeneficiarioAntecessor")
                                .HasColumnName("CODIGO_BENEFICIARIO_ORIGINARIO");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("DataSucessao")
                                .HasColumnName("DATA_SUCESSAO");

                            t.Property("IndicadorIsencaoIRRF")
                                .HasColumnName("IND_ISENTO_IRRF");

                            t.Property("IndicadorOrdemPagamento107AParcela01")
                                .HasColumnName("IND_ORDEM_PAGAMENTO_107A_PARC01");

                            t.Property("IndicadorOrdemPagamento107AParcela02")
                                .HasColumnName("IND_ORDEM_PAGAMENTO_107A_PARC02");

                            t.Property("IndicadorOrdemPagamento107AParcela03")
                                .HasColumnName("IND_ORDEM_PAGAMENTO_107A_PARC03");

                            t.Property("IndicadorPortadorDeficiencia")
                                .HasColumnName("IND_PORTADOR_DEFICIENCIA");

                            t.Property("NomeBeneficiario")
                                .HasColumnName("NOME_BENEFICIARIO");

                            t.Property("NomeBeneficiarioAntecessor")
                                .HasColumnName("NOME_BENEFICIARIO_ORIGINARIO");

                            t.Property("NomeSocialBeneficiario")
                                .HasColumnName("NOME_SOCIAL");

                            t.Property("SucessaoTipo")
                                .HasColumnName("TIPO_SUCESSAO");

                            t.Property("ValorIndividualExpedicao");

                            t.Property("ValorIndividualUltimoCalculo");

                            t.Property("ValorJurosSelic1");

                            t.Property("ValorJurosSelic10");

                            t.Property("ValorJurosSelic2");

                            t.Property("ValorJurosSelic3");

                            t.Property("ValorJurosSelic4");

                            t.Property("ValorJurosSelic5");

                            t.Property("ValorJurosSelic6");

                            t.Property("ValorJurosSelic7");

                            t.Property("ValorJurosSelic8");

                            t.Property("ValorJurosSelic9");

                            t.Property("ValorPrincipalTRF1");

                            t.Property("ValorPrincipalTRF10");

                            t.Property("ValorPrincipalTRF2");

                            t.Property("ValorPrincipalTRF3");

                            t.Property("ValorPrincipalTRF4");

                            t.Property("ValorPrincipalTRF5");

                            t.Property("ValorPrincipalTRF6");

                            t.Property("ValorPrincipalTRF7");

                            t.Property("ValorPrincipalTRF8");

                            t.Property("ValorPrincipalTRF9");
                        });
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiariosOrigensRPV.BeneficiarioOrigemRPV", b =>
                {
                    b.Property<int>("ControleProcessamentoId")
                        .HasColumnType("int")
                        .HasColumnName("CONTROLEPROCESSAMENTOID");

                    b.Property<string>("NumeroProcesso")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUMEROPROCESSO");

                    b.Property<string>("CodigoBeneficiario")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("CODIGOBENEFICIARIO");

                    b.Property<short>("AnoExercicioCorrente")
                        .HasColumnType("smallint")
                        .HasColumnName("ANOEXERCICIOCORRENTE");

                    b.Property<int>("AnoProposta")
                        .HasColumnType("int")
                        .HasColumnName("ANOPROPOSTA");

                    b.Property<int?>("CodigoAgencia")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOAGENCIA");

                    b.Property<int?>("CodigoBanco")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOBANCO");

                    b.Property<string>("CodigoBeneficiarioAntecessor")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGOBENEFICIARIOANTECESSOR");

                    b.Property<int>("CodigoUORCadastradora")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOUORCADASTRADORA");

                    b.Property<string>("CondicaoServidor")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CONDICAOSERVIDOR");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAALTERACAO");

                    b.Property<DateTime>("DataExtracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAEXTRACAO");

                    b.Property<DateTime?>("DataImportacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAIMPORTACAO");

                    b.Property<DateTime?>("DataInclusao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAINCLUSAO");

                    b.Property<DateTime?>("DataSucessao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATASUCESSAO");

                    b.Property<string>("IdentificadorPV")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDENTIFICADORPV");

                    b.Property<string>("IndicadorIsencaoIRRF")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORISENCAOIRRF");

                    b.Property<string>("IndicadorOrdemPagamento107AParcela01")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORORDEMPAGAMENTO107APARCELA01");

                    b.Property<string>("IndicadorOrdemPagamento107AParcela02")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORORDEMPAGAMENTO107APARCELA02");

                    b.Property<string>("IndicadorOrdemPagamento107AParcela03")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORORDEMPAGAMENTO107APARCELA03");

                    b.Property<string>("IndicadorPortadorDeficiencia")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORPORTADORDEFICIENCIA");

                    b.Property<string>("NomeBeneficiario")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEBENEFICIARIO");

                    b.Property<string>("NomeBeneficiarioAntecessor")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEBENEFICIARIOANTECESSOR");

                    b.Property<string>("NomeSocialBeneficiario")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMESOCIALBENEFICIARIO");

                    b.Property<string>("NumeroConta")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROCONTA");

                    b.Property<int?>("NumeroMesesExerciciosAnteriores")
                        .HasColumnType("int")
                        .HasColumnName("NUMEROMESESEXERCICIOSANTERIORES");

                    b.Property<int?>("NumeroMesesExerciciosCorrentes")
                        .HasColumnType("int")
                        .HasColumnName("NUMEROMESESEXERCICIOSCORRENTES");

                    b.Property<string>("SucessaoTipo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SUCESSAOTIPO");

                    b.Property<string>("TipoBeneficiario")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOBENEFICIARIO");

                    b.Property<string>("TipoMovimento")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOMOVIMENTO");

                    b.Property<int?>("UOLotacao")
                        .HasColumnType("int")
                        .HasColumnName("UOLOTACAO");

                    b.Property<string>("UsuarioAlteracao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("USUARIOALTERACAO");

                    b.Property<string>("UsuarioInclusao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("USUARIOINCLUSAO");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA1");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA10");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA2");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA3");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA4");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA5");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA6");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA7");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA8");

                    b.Property<decimal?>("ValorCompensacaoPSSParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_COMP_PSS_PARCELA9");

                    b.Property<decimal?>("ValorDeducoesIndividuais")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("DEDUCOES_INDIVIDUAIS");

                    b.Property<decimal?>("ValorExercicioCorrente")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_EXERCICIO_CORRENTE");

                    b.Property<decimal?>("ValorExerciciosAnteriores")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_EXERCICIOS_ANTERIORES");

                    b.Property<decimal>("ValorIndividual")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_INDIVIDUAL");

                    b.Property<decimal?>("ValorIndividualExpedicao")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_INDIVIDUAL_EXPEDICAO");

                    b.Property<decimal?>("ValorIndividualUltimoCalculo")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_INDIVIDUAL_ULTIMO_CALCULO");

                    b.Property<decimal?>("ValorJurosSelic1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_1");

                    b.Property<decimal?>("ValorJurosSelic10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_10");

                    b.Property<decimal?>("ValorJurosSelic2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_2");

                    b.Property<decimal?>("ValorJurosSelic3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_3");

                    b.Property<decimal?>("ValorJurosSelic4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_4");

                    b.Property<decimal?>("ValorJurosSelic5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_5");

                    b.Property<decimal?>("ValorJurosSelic6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_6");

                    b.Property<decimal?>("ValorJurosSelic7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_7");

                    b.Property<decimal?>("ValorJurosSelic8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_8");

                    b.Property<decimal?>("ValorJurosSelic9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_JUROS_SELIC_9");

                    b.Property<decimal?>("ValorPSSParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA1");

                    b.Property<decimal?>("ValorPSSParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA10");

                    b.Property<decimal?>("ValorPSSParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA2");

                    b.Property<decimal?>("ValorPSSParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA3");

                    b.Property<decimal?>("ValorPSSParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA4");

                    b.Property<decimal?>("ValorPSSParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA5");

                    b.Property<decimal?>("ValorPSSParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA6");

                    b.Property<decimal?>("ValorPSSParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA7");

                    b.Property<decimal?>("ValorPSSParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA8");

                    b.Property<decimal?>("ValorPSSParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_PARCELA9");

                    b.Property<decimal?>("ValorParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA1");

                    b.Property<decimal?>("ValorParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA10");

                    b.Property<decimal?>("ValorParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA2");

                    b.Property<decimal?>("ValorParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA3");

                    b.Property<decimal?>("ValorParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA4");

                    b.Property<decimal?>("ValorParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA5");

                    b.Property<decimal?>("ValorParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA6");

                    b.Property<decimal?>("ValorParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA7");

                    b.Property<decimal?>("ValorParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA8");

                    b.Property<decimal?>("ValorParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA9");

                    b.Property<decimal?>("ValorPrincipalTRF1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_1");

                    b.Property<decimal?>("ValorPrincipalTRF10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_10");

                    b.Property<decimal?>("ValorPrincipalTRF2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_2");

                    b.Property<decimal?>("ValorPrincipalTRF3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_3");

                    b.Property<decimal?>("ValorPrincipalTRF4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_4");

                    b.Property<decimal?>("ValorPrincipalTRF5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_5");

                    b.Property<decimal?>("ValorPrincipalTRF6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_6");

                    b.Property<decimal?>("ValorPrincipalTRF7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_7");

                    b.Property<decimal?>("ValorPrincipalTRF8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_8");

                    b.Property<decimal?>("ValorPrincipalTRF9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PRINCIPAL_TRF_9");

                    b.HasKey("ControleProcessamentoId", "NumeroProcesso", "CodigoBeneficiario");

                    b.ToTable("RPV_BENEFICIARIOS", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("CodigoBeneficiario")
                                .HasColumnName("CODIGO_BENEFICIARIO");

                            t.Property("AnoExercicioCorrente")
                                .HasColumnName("ANO_EXERCICIO_CORRENTE");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CodigoAgencia")
                                .HasColumnName("CODIGO_AGENCIA");

                            t.Property("CodigoBanco")
                                .HasColumnName("CODIGO_BANCO");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("CondicaoServidor")
                                .HasColumnName("CONDICAO_SERVIDOR");

                            t.Property("DataAlteracao")
                                .HasColumnName("DATA_ALTERACAO");

                            t.Property("DataExtracao")
                                .HasColumnName("DAT_EXTRAC");

                            t.Property("DataImportacao")
                                .HasColumnName("DAT_IMPORT");

                            t.Property("DataInclusao")
                                .HasColumnName("DATA_INCLUSAO");

                            t.Property("IdentificadorPV")
                                .HasColumnName("IDENTIFICACAO_PV");

                            t.Property("NomeBeneficiario")
                                .HasColumnName("NOME_BENEFICIARIO");

                            t.Property("NumeroConta")
                                .HasColumnName("NUMERO_CONTA");

                            t.Property("NumeroMesesExerciciosAnteriores")
                                .HasColumnName("NM_EXERCICIOS_ANTERIORES");

                            t.Property("NumeroMesesExerciciosCorrentes")
                                .HasColumnName("NM_EXERCICIO_CORRENTE");

                            t.Property("TipoBeneficiario")
                                .HasColumnName("TIPO_BENEFICIARIO");

                            t.Property("TipoMovimento")
                                .HasColumnName("TIPO_MOVIMENTO");

                            t.Property("UOLotacao")
                                .HasColumnName("UO_LOTACAO");

                            t.Property("UsuarioAlteracao")
                                .HasColumnName("USUARIO_ALTERACAO");

                            t.Property("UsuarioInclusao")
                                .HasColumnName("USUARIO_INCLUSAO");
                        });

                    b.SplitToTable("RPV_BENEFICIARIOS2", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("CodigoBeneficiario")
                                .HasColumnName("CODIGO_BENEFICIARIO");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CodigoBeneficiarioAntecessor")
                                .HasColumnName("CODIGO_BENEFICIARIO_ORIGINARIO");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("DataSucessao")
                                .HasColumnName("DATA_SUCESSAO");

                            t.Property("IndicadorIsencaoIRRF")
                                .HasColumnName("IND_ISENTO_IRRF");

                            t.Property("IndicadorOrdemPagamento107AParcela01")
                                .HasColumnName("IND_ORDEM_PAGAMENTO_107A_PARC01");

                            t.Property("IndicadorOrdemPagamento107AParcela02")
                                .HasColumnName("IND_ORDEM_PAGAMENTO_107A_PARC02");

                            t.Property("IndicadorOrdemPagamento107AParcela03")
                                .HasColumnName("IND_ORDEM_PAGAMENTO_107A_PARC03");

                            t.Property("IndicadorPortadorDeficiencia")
                                .HasColumnName("IND_PORTADOR_DEFICIENCIA");

                            t.Property("NomeBeneficiario")
                                .HasColumnName("NOME_BENEFICIARIO");

                            t.Property("NomeBeneficiarioAntecessor")
                                .HasColumnName("NOME_BENEFICIARIO_ORIGINARIO");

                            t.Property("NomeSocialBeneficiario")
                                .HasColumnName("NOME_SOCIAL");

                            t.Property("SucessaoTipo")
                                .HasColumnName("TIPO_SUCESSAO");

                            t.Property("ValorIndividualExpedicao");

                            t.Property("ValorIndividualUltimoCalculo");

                            t.Property("ValorJurosSelic1");

                            t.Property("ValorJurosSelic10");

                            t.Property("ValorJurosSelic2");

                            t.Property("ValorJurosSelic3");

                            t.Property("ValorJurosSelic4");

                            t.Property("ValorJurosSelic5");

                            t.Property("ValorJurosSelic6");

                            t.Property("ValorJurosSelic7");

                            t.Property("ValorJurosSelic8");

                            t.Property("ValorJurosSelic9");

                            t.Property("ValorPrincipalTRF1");

                            t.Property("ValorPrincipalTRF10");

                            t.Property("ValorPrincipalTRF2");

                            t.Property("ValorPrincipalTRF3");

                            t.Property("ValorPrincipalTRF4");

                            t.Property("ValorPrincipalTRF5");

                            t.Property("ValorPrincipalTRF6");

                            t.Property("ValorPrincipalTRF7");

                            t.Property("ValorPrincipalTRF8");

                            t.Property("ValorPrincipalTRF9");
                        });
                });

            modelBuilder.Entity("TRF3.SISPREC.BlocosSisprec.BlocoSisprec", b =>
                {
                    b.Property<int>("BlocoSisprecId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_BLOCO_SISPREC");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BlocoSisprecId"));

                    b.Property<DateTime>("DataCriacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CRIACA");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("NomeUsuario")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar")
                        .HasColumnName("NOM_USUARI");

                    b.Property<string>("StatusBloco")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar")
                        .HasColumnName("STA_BLOCO");

                    b.HasKey("BlocoSisprecId")
                        .HasName("ANA_BLOCO_SISPREC_P01");

                    b.ToTable("ANA_BLOCO_SISPREC", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.CidadesRegPag.CidadeRegPag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("COD_CIDADE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("MunicipioId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MUNICI");

                    b.HasKey("Id")
                        .HasName("SIN_CIDADE_REQPAG_P01");

                    b.ToTable("SIN_CIDADE_REQPAG", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.CodigosReceitaFederal.CodigoReceitaFederal", b =>
                {
                    b.Property<int>("CodigoReceitaFederalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CODIGO_RECEIT_FEDERA");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CodigoReceitaFederalId"));

                    b.Property<bool>("Ativo")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("CodReceitaFederal")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("nvarchar(7)")
                        .HasColumnName("COD_RECEIT_FEDERA");

                    b.Property<string>("CodigoTipoGuia")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("nvarchar(1)")
                        .HasColumnName("COD_TIPO_GUIA");

                    b.Property<string>("DescricaoResumo")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("DES_RESUMO_RECEIT_FEDERA");

                    b.Property<string>("DescricaoTitulo")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("DES_TITULO_RECEIT_FEDERA");

                    b.HasKey("CodigoReceitaFederalId")
                        .HasName("TRF_CODIGO_RECEITA_FEDERAL_P01");

                    b.HasIndex("CodReceitaFederal", "CodigoTipoGuia")
                        .IsUnique()
                        .HasDatabaseName("TRF_CODIGO_RECEITA_FEDERAL_U01");

                    b.ToTable("TRF_CODIGO_RECEITA_FEDERAL", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ContaBancariaBeneficiarios.ContaBancariaBeneficiario", b =>
                {
                    b.Property<long>("Seq_Conta_Bancar")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_CONTA_BANCAR");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Seq_Conta_Bancar"));

                    b.Property<int>("Agencia")
                        .HasColumnType("int")
                        .HasColumnName("NUM_AGENCI");

                    b.Property<int>("Banco")
                        .HasColumnType("int")
                        .HasColumnName("NUM_BANCO");

                    b.Property<string>("ContaCorrente")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasColumnName("NUM_CC");

                    b.HasKey("Seq_Conta_Bancar")
                        .HasName("CJF_BENEFICIARIO_CONTA_BANCARIA_P01");

                    b.ToTable("CJF_BENEFICIARIO_CONTA_BANCARIA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ContaBancarias.ContaBancaria", b =>
                {
                    b.Property<long>("Seq_Conta_Bancar")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_CONTA_BANCAR");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Seq_Conta_Bancar"));

                    b.Property<int>("Agencia")
                        .HasColumnType("int")
                        .HasColumnName("NUM_AGENCI");

                    b.Property<int>("Banco")
                        .HasColumnType("int")
                        .HasColumnName("NUM_BANCO");

                    b.Property<string>("ContaCorrente")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasColumnName("NUM_CC");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Conta_Bancar")
                        .HasName("CJF_PROCESSO_CONTA_BANCARIA_P01");

                    b.ToTable("CJF_PROCESSO_CONTA_BANCARIA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleImportacaoRequisicaoErros.ControleImportacaoRequisicaoErro", b =>
                {
                    b.Property<int>("ControleImportacaoRequisicaoErroId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_IMPORT_ERRO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ControleImportacaoRequisicaoErroId"));

                    b.Property<int>("ControleImportacaoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_IMPORT");

                    b.Property<DateTime>("DataErro")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_ERRO");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_ERRO");

                    b.HasKey("ControleImportacaoRequisicaoErroId")
                        .HasName("APP_CONTROLE_IMPORTACAO_REQUISICOES_ERRO_P01");

                    b.ToTable("APP_CONTROLE_IMPORTACAO_REQUISICOES_ERRO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleImportacaoRequisicoes.ControleImportacaoRequisicao", b =>
                {
                    b.Property<int>("ControleImportacaoRequisicaoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_IMPORT");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ControleImportacaoRequisicaoId"));

                    b.Property<string>("NumeroProtocoloRequisicao")
                        .IsRequired()
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_REQUIS_PROTOC");

                    b.Property<int>("PropostaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_PROPOS");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("char(30)")
                        .HasColumnName("STA_IMPORT");

                    b.HasKey("ControleImportacaoRequisicaoId")
                        .HasName("APP_CONTROLE_IMPORTACAO_REQUISICOES_P01");

                    b.ToTable("APP_CONTROLE_IMPORTACAO_REQUISICOES", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleProcessamentoArquivos.ControleProcessamentoArquivo", b =>
                {
                    b.Property<int>("ControleProcessamentoArquivoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_PROCES_ARQUIV");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ControleProcessamentoArquivoId"));

                    b.Property<string>("CaminhoArquivo")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("DES_MDB_CAMINH");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CADAST");

                    b.Property<DateTime?>("DataExclusao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_EXCLUS");

                    b.HasKey("ControleProcessamentoArquivoId")
                        .HasName("APP_CONTROLE_PROCESSAMENTO_ARQUIVO_P01");

                    b.ToTable("APP_CONTROLE_PROCESSAMENTO_ARQUIVO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleProcessamentoProcessos.ControleProcessamentoProcesso", b =>
                {
                    b.Property<int>("ControleProcessamentoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_PROCES");

                    b.Property<string>("NumeroProcesso")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUM_PROCES");

                    b.Property<DateTime>("DataAtualizacao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_ATUALI")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime>("DataCriacao")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CRIACA")
                        .HasDefaultValueSql("CURRENT_TIMESTAMP");

                    b.Property<DateTime?>("DataExclusao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_EXCLUS");

                    b.Property<string>("Erro")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_ERRO_JSON");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("IDE_STATUS");

                    b.HasKey("ControleProcessamentoId", "NumeroProcesso")
                        .HasName("APP_CONTROLE_PROCESSAMENTO_PROCESSO_P01");

                    b.ToTable("APP_CONTROLE_PROCESSAMENTO_PROCESSO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_PROCES");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ArquivoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_PROCES_ARQUIV");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CADAST");

                    b.Property<DateTime?>("DataConclusao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CONCLU");

                    b.Property<DateTime?>("DataExclusao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_EXCLUS");

                    b.Property<string>("Erro")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_ERRO");

                    b.Property<int>("EtapaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_TIPO_ETAPA");

                    b.Property<int>("FaseId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_FASE");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("IDE_STATUS");

                    b.HasKey("Id")
                        .HasName("APP_CONTROLE_PROCESSAMENTO_P01");

                    b.HasIndex("EtapaId")
                        .HasDatabaseName("APP_CONTROLE_PROCESSAMENTO_i01");

                    b.HasIndex("FaseId")
                        .HasDatabaseName("APP_CONTROLE_PROCESSAMENTO_i02");

                    b.ToTable("APP_CONTROLE_PROCESSAMENTO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.DespesaClassificacoes.DespesaClassificacao", b =>
                {
                    b.Property<int>("Seq_Classi_Despesa")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CLASSI_DESPES");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Classi_Despesa"));

                    b.Property<bool>("Alimentar")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_ALIMEN");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<int>("DespesaNaturezaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_NATURE_DESPES");

                    b.Property<int>("DespesaTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_TIPO_DESPES");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Classi_Despesa")
                        .HasName("CJF_DESPESA_CLASSIFICACAO_P01");

                    b.ToTable("CJF_DESPESA_CLASSIFICACAO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.DespesaNaturezas.DespesaNatureza", b =>
                {
                    b.Property<int>("Seq_Nature_Despesa")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_NATURE_DESPES");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Nature_Despesa"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_NATURE_DESPES");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_NATURE_DESPES");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Nature_Despesa")
                        .HasName("CJF_DESPESA_NATUREZA_P01");

                    b.ToTable("CJF_DESPESA_NATUREZA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.DespesaTipos.DespesaTipo", b =>
                {
                    b.Property<int>("Seq_Tipo_Despesa")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_TIPO_DESPES");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Tipo_Despesa"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_TIPO_DESPES");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_TIPO_DESPES");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Tipo_Despesa")
                        .HasName("CJF_DESPESA_TIPO_P01");

                    b.ToTable("CJF_DESPESA_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.DivergenciaNomePartes.DivergenciaNomeParte", b =>
                {
                    b.Property<int>("DivergenciaNomeParteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_DIVERG");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DivergenciaNomeParteId"));

                    b.Property<int>("Ano")
                        .HasColumnType("int")
                        .HasColumnName("ANO_PROPOS");

                    b.Property<string>("CpfCnpj")
                        .IsRequired()
                        .HasMaxLength(14)
                        .HasColumnType("varchar(14)")
                        .HasColumnName("NUM_CNPJ_CPF");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<bool>("Lida")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_LIDA");

                    b.Property<int>("Mes")
                        .HasColumnType("int")
                        .HasColumnName("MES_PROPOS");

                    b.Property<string>("TipoProcedimento")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("CHAR(3)")
                        .HasColumnName("COD_TIPO_PROCED");

                    b.HasKey("DivergenciaNomeParteId")
                        .HasName("ANA_DIVERGENCIA_NOME_PARTE_P01");

                    b.ToTable("ANA_DIVERGENCIA_NOME_PARTE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ExpedientesAdministrativos.ExpedienteAdministrativo", b =>
                {
                    b.Property<int>("ExpedienteAdministrativoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_EXPEDI_ADMINI");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ExpedienteAdministrativoId"));

                    b.Property<int?>("BlocoSisprecId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_BLOCO_SISPREC");

                    b.Property<DateTime?>("DataExpedienteAdministrativo")
                        .IsRequired()
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_EXPEDI_ADMINI");

                    b.Property<string>("IdentificadorProcessoSei")
                        .HasMaxLength(20)
                        .HasColumnType("varchar")
                        .HasColumnName("IDE_PROCES_SEI");

                    b.Property<string>("NomeUsuario")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("NOM_USUARI");

                    b.Property<int?>("NumeroExpedienteAdministrativo")
                        .HasColumnType("int")
                        .HasColumnName("NUM_EXPEDI_ADMINI");

                    b.Property<string>("NumeroProcessoSei")
                        .HasMaxLength(20)
                        .HasColumnType("varchar")
                        .HasColumnName("NUM_PROCES_SEI");

                    b.Property<string>("ObservacaoExpedienteAdministrativo")
                        .IsRequired()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("DES_OBSERV_EXPEDI_ADMINI");

                    b.Property<string>("StatusExpedienteAdminstrativo")
                        .HasMaxLength(30)
                        .HasColumnType("varchar")
                        .HasColumnName("STA_EXPEDI_ADMINI");

                    b.Property<string>("TipoExpedienteAdministrativo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIP_EXPEDI_ADMINI");

                    b.Property<string>("TipoProcessoSei")
                        .HasMaxLength(30)
                        .HasColumnType("varchar")
                        .HasColumnName("IDE_TIPO_PROCES_SEI");

                    b.HasKey("ExpedienteAdministrativoId")
                        .HasName("ANA_EXPEDIENTE_ADMINISTRATIVO_P01");

                    b.ToTable("ANA_EXPEDIENTE_ADMINISTRATIVO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ExpedientesAdministrativosHistorico.ExpedienteAdministrativoHistorico", b =>
                {
                    b.Property<int>("ExpedienteAdministrativoHistoricoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_EXPEDI_ADMINI_HISTOR");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ExpedienteAdministrativoHistoricoId"));

                    b.Property<DateTime>("DataStatus")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_STATUS");

                    b.Property<int>("ExpedienteAdministrativoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_EXPEDI_ADMINI");

                    b.Property<string>("NomeUsuario")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar")
                        .HasColumnName("NOM_USUARI");

                    b.Property<string>("StatusAdministrativo")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar")
                        .HasColumnName("STA_EXPEDI_ADMINI");

                    b.HasKey("ExpedienteAdministrativoHistoricoId")
                        .HasName("ANA_EXPEDIENTE_ADMINISTRATIVO_HISTORICO_P01");

                    b.ToTable("ANA_EXPEDIENTE_ADMINISTRATIVO_HISTORICO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.FaseTipos.FaseTipo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_FASE_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("COD_FASE_TIPO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_FASE_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("NumeroParcela")
                        .HasColumnType("int")
                        .HasColumnName("NUM_PARCEL");

                    b.Property<string>("Observacao")
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_OBSERV");

                    b.Property<string>("Origem")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_ORIGEM");

                    b.Property<int?>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Id")
                        .HasName("CJF_FASE_TIPO_P01");

                    b.ToTable("CJF_FASE_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Fases.Fase", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_FASE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AnoReferencia")
                        .HasColumnType("int")
                        .HasColumnName("ANO");

                    b.Property<DateTime>("DataAbertura")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_ABERTU");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_CADAST");

                    b.Property<DateTime>("DataFechamento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_FECHAM");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int>("MesReferencia")
                        .HasColumnType("int")
                        .HasColumnName("MES");

                    b.Property<int>("SEQ_UG")
                        .HasColumnType("int");

                    b.Property<int>("Seq_Fase_Tipo")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_FASE_TIPO");

                    b.Property<int>("Seq_Plano")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_PLANO");

                    b.Property<int?>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.Property<string>("TipoPrecatorio")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPO");

                    b.HasKey("Id")
                        .HasName("CJF_FASE_P01");

                    b.HasIndex("Seq_Fase_Tipo")
                        .HasDatabaseName("CJF_FASE_i02");

                    b.HasIndex("Seq_Plano")
                        .HasDatabaseName("CJF_FASE_i01");

                    b.ToTable("CJF_FASE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.IndicadorEconomicoTipos.IndicadorEconomicoTipo", b =>
                {
                    b.Property<int>("TipoIndicadorEconomicoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICA_ECONOM_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TipoIndicadorEconomicoId"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("CHAR(3)")
                        .HasColumnName("COD_INDICA_ECONOM_TIPO");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("DES_INDICA_ECONOM_TIPO");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.HasKey("TipoIndicadorEconomicoId")
                        .HasName("TRF_INDICADOR_ECONOMICO_TIPO_P01");

                    b.HasIndex("Codigo")
                        .IsUnique()
                        .HasDatabaseName("TRF_INDICADOR_ECONOMICO_TIPO_U01");

                    b.ToTable("TRF_INDICADOR_ECONOMICO_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.IndicadorEconomicos.IndicadorEconomico", b =>
                {
                    b.Property<int>("IndicadorEconomicoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICA_ECONOM");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("IndicadorEconomicoId"));

                    b.Property<DateTime>("DataIndicador")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_INDICA_ECONOM");

                    b.Property<int>("IndicadorEconomicoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICA_ECONOM_TIPO");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<decimal?>("PercentualInflacionario")
                        .HasPrecision(7, 4)
                        .HasColumnType("decimal(7,4)")
                        .HasColumnName("PEC_INFLAC");

                    b.Property<string>("SiglaMoeda")
                        .HasColumnType("VARCHAR(4)")
                        .HasColumnName("SIG_MOEDA");

                    b.Property<decimal>("Valor")
                        .HasPrecision(31, 10)
                        .HasColumnType("decimal(31,10)")
                        .HasColumnName("VAL_INDICA_ECONOM");

                    b.HasKey("IndicadorEconomicoId")
                        .HasName("TRF_INDICADOR_ECONOMICO_P01");

                    b.HasIndex("IndicadorEconomicoTipoId", "DataIndicador")
                        .IsUnique()
                        .HasDatabaseName("TRF_INDICADOR_ECONOMICO_U01");

                    b.ToTable("TRF_INDICADOR_ECONOMICO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos.IndiceAtualizacaoMonetariaTipo", b =>
                {
                    b.Property<int>("Seq_Indice_Atuali_Moneta_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICE_ATUALI_MONETA_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Indice_Atuali_Moneta_Tipo"));

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_INDICE_ATUALI_TIPO");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_INDICE_ATUALI_TIPO");

                    b.Property<string>("DescricaoUsoCodigo")
                        .HasColumnType("varchar(50)")
                        .HasColumnName("DES_USO_COD");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Indice_Atuali_Moneta_Tipo")
                        .HasName("CJF_INDICE_ATUALI_MONETA_TIPO_P01");

                    b.ToTable("CJF_INDICE_ATUALI_MONETA_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.IndiceAtualizacaoMonetarias.IndiceAtualizacaoMonetaria", b =>
                {
                    b.Property<int>("Seq_Indice_Atuali_Moneta")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICE_ATUALI_MONETA");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Indice_Atuali_Moneta"));

                    b.Property<int?>("Ano")
                        .HasColumnType("int")
                        .HasColumnName("ANO");

                    b.Property<DateTime?>("DataRegistro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_VALIDA_CADAST");

                    b.Property<DateTime?>("DataValidadeFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_VALIDA_FIM");

                    b.Property<DateTime?>("DataValidadeInicio")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_VALIDA_INICIO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("Mes")
                        .HasColumnType("int")
                        .HasColumnName("MES");

                    b.Property<int?>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.Property<int>("TipoIndiceId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICE_ATUALI_MONETA_TIPO");

                    b.Property<decimal?>("ValIndicaEconom")
                        .HasColumnType("decimal")
                        .HasColumnName("VAL_INDICA_ECONOM");

                    b.Property<decimal?>("Valor")
                        .HasColumnType("decimal")
                        .HasColumnName("VAL_INDICE");

                    b.HasKey("Seq_Indice_Atuali_Moneta")
                        .HasName("CJF_INDICE_ATUALI_MONETA_P01");

                    b.ToTable("CJF_INDICE_ATUALI_MONETA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.JustificativaComparacoes.JustificativaComparacao", b =>
                {
                    b.Property<long>("RequisicaoJustificativaId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_JUSTIF");

                    b.Property<string>("NumeroProtocoloRequisicao")
                        .HasMaxLength(20)
                        .HasColumnType("varchar")
                        .HasColumnName("NUM_PROTOC_REQUIS_COMPAR");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<long?>("JustificativaDocumentoId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_JUSTIF_DOCUME_ESPELH");

                    b.Property<string>("Observacoes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_OBSERV");

                    b.HasKey("RequisicaoJustificativaId", "NumeroProtocoloRequisicao")
                        .HasName("ANA_JUSTIFICATIVA_COMPARACAO_P01");

                    b.ToTable("ANA_JUSTIFICATIVA_COMPARACAO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.JustificativaDocumentos.JustificativaDocumento", b =>
                {
                    b.Property<long>("JustificativaDocumentoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_JUSTIF_DOCUME");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("JustificativaDocumentoId"));

                    b.Property<DateTime>("DataCriacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CRIACA");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("NomeDocumento")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar")
                        .HasColumnName("NOM_DOCUME");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar")
                        .HasColumnName("DES_PATH");

                    b.Property<long>("RequisicaoJustificativaId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_JUSTIF");

                    b.Property<string>("TipoDocumentoJustificativa")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("varchar")
                        .HasColumnName("IDE_TIPO_DOCUME");

                    b.HasKey("JustificativaDocumentoId")
                        .HasName("ANA_JUSTIFICATIVA_DOCUMENTO_P01");

                    b.ToTable("ANA_JUSTIFICATIVA_DOCUMENTO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.LogDetalhes.LogDetalhe", b =>
                {
                    b.Property<long>("Seq_Log_Detalhes")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_LOG_DETALH");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Seq_Log_Detalhes"));

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_LOG");

                    b.Property<long>("LogId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_LOG");

                    b.HasKey("Seq_Log_Detalhes")
                        .HasName("APP_LOG_GERAL_DETALHES_P01");

                    b.ToTable("APP_LOG_GERAL_DETALHES", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.LogGerais.LogGeral", b =>
                {
                    b.Property<long>("Seq_Log")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_LOG");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Seq_Log"));

                    b.Property<string>("Cabecalho")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_CABECA");

                    b.Property<DateTime>("DataRegistro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_REGIST");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(500)")
                        .HasColumnName("DES_LOG");

                    b.Property<string>("GUID")
                        .IsRequired()
                        .HasColumnType("varchar(128)")
                        .HasColumnName("DES_GUID");

                    b.Property<string>("Mode")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasColumnName("DES_MODE");

                    b.Property<string>("PID")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasColumnName("DES_PID");

                    b.Property<int>("StatusCode")
                        .HasColumnType("int")
                        .HasColumnName("COD_STATUS");

                    b.HasKey("Seq_Log")
                        .HasName("APP_LOG_GERAL_P01");

                    b.ToTable("APP_LOG_GERAL", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ModelosDocumentos.ModeloDocumento", b =>
                {
                    b.Property<int>("ModeloDocumentoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MODELO_DOCUME");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ModeloDocumentoId"));

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("NomeModelo")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar")
                        .HasColumnName("NOM_MODELO_DOCUME");

                    b.Property<int>("SetorId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SETOR");

                    b.Property<string>("TextoDocumento")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TXT_MODELO_DOCUME");

                    b.HasKey("ModeloDocumentoId")
                        .HasName("ANA_MODELO_DOCUMENTO_P01");

                    b.ToTable("ANA_MODELO_DOCUMENTO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.MotivosExpedientesAdministrativos.MotivoExpedienteAdministrativo", b =>
                {
                    b.Property<int>("ExpedienteAdministrativoMotivoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_EXPEDI_ADMINI_MOTIVO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ExpedienteAdministrativoMotivoId"));

                    b.Property<string>("DescricaoMotivo")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("varchar")
                        .HasColumnName("DES_MOTIVO");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.HasKey("ExpedienteAdministrativoMotivoId")
                        .HasName("ANA_EXPEDIENTE_ADMINISTRATIVO_MOTIVO_P01");

                    b.ToTable("ANA_EXPEDIENTE_ADMINISTRATIVO_MOTIVO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.MovimentoTipos.MovimentoTipo", b =>
                {
                    b.Property<int>("Seq_Movime_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MOVIME_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Movime_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_MOVIME_TIPO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_MOVIME_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("Sequencial")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Movime_Tipo")
                        .HasName("CJF_MOVIME_TIPO_P01");

                    b.ToTable("CJF_MOVIME_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Municipios.Municipio", b =>
                {
                    b.Property<int>("MunicipioId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MUNICI");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MunicipioId"));

                    b.Property<int>("CodIBGE")
                        .HasColumnType("int")
                        .HasColumnName("COD_IBGE");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("NOM_MUNICI");

                    b.Property<string>("SiglaUF")
                        .IsRequired()
                        .HasColumnType("varchar(2)")
                        .HasColumnName("SIG_UF");

                    b.HasKey("MunicipioId")
                        .HasName("TRF_MUNICIPIO_P01");

                    b.ToTable("TRF_MUNICIPIO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.OcorrenciaMotivos.OcorrenciaMotivo", b =>
                {
                    b.Property<int>("OcorrenciaMotivoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MOTIVO_OCORRE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OcorrenciaMotivoId"));

                    b.Property<int>("AcaoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ACAO_TIPO");

                    b.Property<int?>("AnaliseTelaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ANALIS_TELA");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<int>("CodigoMotivo")
                        .HasColumnType("int")
                        .HasColumnName("COD_MOTIVO_OCORRE");

                    b.Property<string>("DescricaoMotivo")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)")
                        .HasColumnName("DES_MOTIVO_OCORRE");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.HasKey("OcorrenciaMotivoId")
                        .HasName("ANA_OCORRENCIA_MOTIVO_P01");

                    b.ToTable("ANA_OCORRENCIA_MOTIVO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.OrdemPagamento107aTipos.OrdemPagamento107aTipo", b =>
                {
                    b.Property<int>("Seq_Ordem_Pagame_107a_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ORDEM_PAGAME_107A_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Ordem_Pagame_107a_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("COD_ORDEM_PAGAME_107A_TIPO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_ORDEM_PAGAME_107A_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<string>("Observacao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_OBSERV");

                    b.Property<string>("SequencialCJF")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Ordem_Pagame_107a_Tipo")
                        .HasName("CJF_ORDEM_PAGAME_107A_TIPO_P01");

                    b.ToTable("CJF_ORDEM_PAGAME_107A_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.PagoBeneficiariosOrigensPCT.PagoBeneficiarioOrigemPCT", b =>
                {
                    b.Property<int>("ControleProcessamentoId")
                        .HasColumnType("int")
                        .HasColumnName("CONTROLEPROCESSAMENTOID");

                    b.Property<string>("NumeroProcesso")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUMEROPROCESSO");

                    b.Property<string>("CodigoBeneficiario")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("CODIGOBENEFICIARIO");

                    b.Property<string>("AcaoOriginaria")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ACAOORIGINARIA");

                    b.Property<int?>("AnoProposta")
                        .HasColumnType("int")
                        .HasColumnName("ANOPROPOSTA");

                    b.Property<int?>("CodigoAgencia")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOAGENCIA");

                    b.Property<int?>("CodigoBanco")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOBANCO");

                    b.Property<string>("CodigoTUA")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGOTUA");

                    b.Property<int?>("CodigoUORCadastradora")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOUORCADASTRADORA");

                    b.Property<string>("CondicaoServidor")
                        .HasColumnType("nvarchar(1)")
                        .HasColumnName("CONDICAOSERVIDOR");

                    b.Property<string>("Conta")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CONTA");

                    b.Property<int?>("ControleProcessamentoDadosBaseId")
                        .HasColumnType("int")
                        .HasColumnName("CONTROLEPROCESSAMENTODADOSBASEID");

                    b.Property<DateTime?>("DataAutuacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAAUTUACAO");

                    b.Property<DateTime>("DataExtracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAEXTRACAO");

                    b.Property<DateTime?>("DataImportacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAIMPORTACAO");

                    b.Property<DateTime?>("DataPagamento")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAPAGAMENTO");

                    b.Property<int?>("ElementoDespesa")
                        .HasColumnType("int")
                        .HasColumnName("ELEMENTODESPESA");

                    b.Property<string>("NomeBeneficiario")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEBENEFICIARIO");

                    b.Property<string>("NumeroConta")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROCONTA");

                    b.Property<string>("TipoCausa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOCAUSA");

                    b.Property<int?>("UOLotacao")
                        .HasColumnType("int")
                        .HasColumnName("UOLOTACAO");

                    b.Property<int?>("UnidadeExecutada")
                        .HasColumnType("int")
                        .HasColumnName("UNIDADEEXECUTADA");

                    b.Property<decimal>("ValorPSSRetido")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_RETIDO");

                    b.Property<decimal?>("ValorPago")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PAGO");

                    b.Property<int?>("Vara")
                        .HasColumnType("int")
                        .HasColumnName("VARA");

                    b.HasKey("ControleProcessamentoId", "NumeroProcesso", "CodigoBeneficiario");

                    b.ToTable("PCT_PAGO_BENEFICIARIOS", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("CodigoBeneficiario")
                                .HasColumnName("CODIGO_BENEFICIARIO");

                            t.Property("AcaoOriginaria")
                                .HasColumnName("ACAO_ORIGINARIA");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CodigoAgencia")
                                .HasColumnName("CODIGO_AGENCIA");

                            t.Property("CodigoBanco")
                                .HasColumnName("CODIGO_BANCO");

                            t.Property("CodigoTUA")
                                .HasColumnName("COD_TUA");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("CondicaoServidor")
                                .HasColumnName("CONDICAO_SERVIDOR");

                            t.Property("Conta")
                                .HasColumnName("CONTA");

                            t.Property("ControleProcessamentoDadosBaseId")
                                .HasColumnName("SEQ_CONTRO_PROCES_BASE");

                            t.Property("DataAutuacao")
                                .HasColumnName("DATA_AUTUACAO");

                            t.Property("DataExtracao")
                                .HasColumnName("DAT_EXTRAC");

                            t.Property("DataImportacao")
                                .HasColumnName("DAT_IMPORT");

                            t.Property("DataPagamento")
                                .HasColumnName("DATA_PAGAMENTO");

                            t.Property("ElementoDespesa")
                                .HasColumnName("ELEMENTO_DESPESA");

                            t.Property("NomeBeneficiario")
                                .HasColumnName("NOME_BENEFICIARIO");

                            t.Property("NumeroConta")
                                .HasColumnName("NUMERO_CONTA");

                            t.Property("TipoCausa")
                                .HasColumnName("TIPO_CAUSA");

                            t.Property("UOLotacao")
                                .HasColumnName("UO_LOTACAO");

                            t.Property("UnidadeExecutada")
                                .HasColumnName("UNIDADE_EXECUTADA");

                            t.Property("Vara")
                                .HasColumnName("VARA");
                        });
                });

            modelBuilder.Entity("TRF3.SISPREC.PagoBeneficiariosOrigensRPV.PagoBeneficiarioOrigemRPV", b =>
                {
                    b.Property<int>("ControleProcessamentoId")
                        .HasColumnType("int")
                        .HasColumnName("CONTROLEPROCESSAMENTOID");

                    b.Property<string>("NumeroProcesso")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUMEROPROCESSO");

                    b.Property<string>("CodigoBeneficiario")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("CODIGOBENEFICIARIO");

                    b.Property<string>("AcaoOriginaria")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ACAOORIGINARIA");

                    b.Property<int?>("AnoProposta")
                        .HasColumnType("int")
                        .HasColumnName("ANOPROPOSTA");

                    b.Property<int?>("CodigoAgencia")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOAGENCIA");

                    b.Property<int?>("CodigoBanco")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOBANCO");

                    b.Property<string>("CodigoTUA")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGOTUA");

                    b.Property<int?>("CodigoUORCadastradora")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOUORCADASTRADORA");

                    b.Property<string>("CondicaoServidor")
                        .HasColumnType("nvarchar(1)")
                        .HasColumnName("CONDICAOSERVIDOR");

                    b.Property<string>("Conta")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CONTA");

                    b.Property<int?>("ControleProcessamentoDadosBaseId")
                        .HasColumnType("int")
                        .HasColumnName("CONTROLEPROCESSAMENTODADOSBASEID");

                    b.Property<DateTime?>("DataAutuacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAAUTUACAO");

                    b.Property<DateTime>("DataExtracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAEXTRACAO");

                    b.Property<DateTime?>("DataImportacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAIMPORTACAO");

                    b.Property<DateTime?>("DataPagamento")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAPAGAMENTO");

                    b.Property<int?>("ElementoDespesa")
                        .HasColumnType("int")
                        .HasColumnName("ELEMENTODESPESA");

                    b.Property<string>("NomeBeneficiario")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEBENEFICIARIO");

                    b.Property<string>("NumeroConta")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROCONTA");

                    b.Property<string>("TipoCausa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOCAUSA");

                    b.Property<int?>("UOLotacao")
                        .HasColumnType("int")
                        .HasColumnName("UOLOTACAO");

                    b.Property<int?>("UnidadeExecutada")
                        .HasColumnType("int")
                        .HasColumnName("UNIDADEEXECUTADA");

                    b.Property<decimal>("ValorPSSRetido")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PSS_RETIDO");

                    b.Property<decimal?>("ValorPago")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PAGO");

                    b.Property<int?>("Vara")
                        .HasColumnType("int")
                        .HasColumnName("VARA");

                    b.HasKey("ControleProcessamentoId", "NumeroProcesso", "CodigoBeneficiario");

                    b.ToTable("RPV_PAGO_BENEFICIARIOS", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("CodigoBeneficiario")
                                .HasColumnName("CODIGO_BENEFICIARIO");

                            t.Property("AcaoOriginaria")
                                .HasColumnName("ACAO_ORIGINARIA");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CodigoAgencia")
                                .HasColumnName("CODIGO_AGENCIA");

                            t.Property("CodigoBanco")
                                .HasColumnName("CODIGO_BANCO");

                            t.Property("CodigoTUA")
                                .HasColumnName("COD_TUA");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("CondicaoServidor")
                                .HasColumnName("CONDICAO_SERVIDOR");

                            t.Property("Conta")
                                .HasColumnName("CONTA");

                            t.Property("ControleProcessamentoDadosBaseId")
                                .HasColumnName("SEQ_CONTRO_PROCES_BASE");

                            t.Property("DataAutuacao")
                                .HasColumnName("DATA_AUTUACAO");

                            t.Property("DataExtracao")
                                .HasColumnName("DAT_EXTRAC");

                            t.Property("DataImportacao")
                                .HasColumnName("DAT_IMPORT");

                            t.Property("DataPagamento")
                                .HasColumnName("DATA_PAGAMENTO");

                            t.Property("ElementoDespesa")
                                .HasColumnName("ELEMENTO_DESPESA");

                            t.Property("NomeBeneficiario")
                                .HasColumnName("NOME_BENEFICIARIO");

                            t.Property("NumeroConta")
                                .HasColumnName("NUMERO_CONTA");

                            t.Property("TipoCausa")
                                .HasColumnName("TIPO_CAUSA");

                            t.Property("UOLotacao")
                                .HasColumnName("UO_LOTACAO");

                            t.Property("UnidadeExecutada")
                                .HasColumnName("UNIDADE_EXECUTADA");

                            t.Property("Vara")
                                .HasColumnName("VARA");
                        });
                });

            modelBuilder.Entity("TRF3.SISPREC.Parcelas.Parcela", b =>
                {
                    b.Property<long>("Seq_Parcela")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PARCEL");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Seq_Parcela"));

                    b.Property<long?>("BeneficiarioId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_BENEFI");

                    b.Property<string>("CodigoReceita")
                        .HasColumnType("varchar(20)")
                        .HasColumnName("COD_RECEIT");

                    b.Property<int?>("CodigoTipoDocumento")
                        .HasColumnType("int")
                        .HasColumnName("COD_TIPO_DOCUME");

                    b.Property<DateTime?>("DataEvento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_EVENT");

                    b.Property<DateTime>("DataRegistro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_REGIST");

                    b.Property<string>("IdentificacaoDebito")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("COD_IDENTI_DEBITO");

                    b.Property<int>("NumeroDaParcela")
                        .HasColumnType("int")
                        .HasColumnName("NUM_PARCEL");

                    b.Property<int?>("OrdemPagamento107aTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ORDEM_PAGAME_107A_TIPO");

                    b.Property<long?>("ProcessoId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PROCES");

                    b.Property<int>("TipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_VALOR_TIPO");

                    b.Property<decimal?>("Valor")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_PARCEL");

                    b.HasKey("Seq_Parcela");

                    b.ToTable("CJF_PARCELAS", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Partes.Parte", b =>
                {
                    b.Property<long>("Seq_Parte")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PARTE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Seq_Parte"));

                    b.Property<long?>("BeneficiarioId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_BENEFI");

                    b.Property<string>("CodTipoParte")
                        .HasColumnType("varchar(100)")
                        .HasColumnName("COD_TIPO_PARTE");

                    b.Property<string>("CpfCnpj")
                        .HasColumnType("varchar(20)")
                        .HasColumnName("COD_CPFCNPJ");

                    b.Property<DateTime?>("DataFalecimento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_FALECI");

                    b.Property<DateTime?>("DataNascimento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_NASCI");

                    b.Property<string>("DesTipoParte")
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_TIPO_PARTE");

                    b.Property<string>("Nome")
                        .HasColumnType("varchar(500)")
                        .HasColumnName("DES_NOME");

                    b.Property<string>("NomeSocial")
                        .HasColumnType("varchar(500)")
                        .HasColumnName("DES_NOME_SOCIAL");

                    b.Property<bool?>("PortadorDeficienciaFisica")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_PORTA_DEF_FIS");

                    b.Property<bool?>("PortadorDoencaGrave")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_PORTA_DOENC_GRAVE");

                    b.Property<long?>("ProcessoId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PROCES");

                    b.Property<string>("RazaoSocial")
                        .HasColumnType("varchar(500)")
                        .HasColumnName("DES_RAZAO_SOCIAL");

                    b.HasKey("Seq_Parte")
                        .HasName("CJF_PARTES_P01");

                    b.ToTable("CJF_PARTES", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Peritos.Perito", b =>
                {
                    b.Property<int>("PeritoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_LISTA_PERITO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PeritoId"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("NomePessoa")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("varchar")
                        .HasColumnName("NOM_PESSOA");

                    b.Property<string>("NumeroCnpjCpf")
                        .IsRequired()
                        .HasMaxLength(14)
                        .HasColumnType("varchar")
                        .HasColumnName("NUM_CNPJ_CPF");

                    b.Property<int>("VerificacaoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_VERIFI_TIPO");

                    b.HasKey("PeritoId")
                        .HasName("TRF_LISTA_PERITO_P01");

                    b.ToTable("TRF_LISTA_PERITO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Pessoas.EnderecosPessoas.EnderecoPessoa", b =>
                {
                    b.Property<long>("PessoaId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PESSOA");

                    b.Property<int?>("Cep")
                        .HasColumnType("int")
                        .HasColumnName("NUM_CEP");

                    b.Property<string>("DescricaoEndereco")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_ENDERE");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<int?>("MunicipioId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MUNICI");

                    b.Property<string>("NomeBairro")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOM_BAIRRO");

                    b.HasKey("PessoaId")
                        .HasName("REQ_PESSOA_ENDERECO_P01");

                    b.ToTable("REQ_PESSOA_ENDERECO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Pessoas.Pessoa", b =>
                {
                    b.Property<long>("PessoaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PESSOA");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("PessoaId"));

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("varchar(220)")
                        .HasColumnName("NOM_PESSOA");

                    b.Property<string>("NomeSocial")
                        .HasColumnType("varchar(220)")
                        .HasColumnName("NOM_SOCIAL");

                    b.Property<string>("NumeroCnpjCpf")
                        .IsRequired()
                        .HasColumnType("varchar(14)")
                        .HasColumnName("NUM_CNPJ_CPF");

                    b.Property<string>("TipoPessoa")
                        .IsRequired()
                        .HasColumnType("char(1)")
                        .HasColumnName("COD_TIPO_PESSOA");

                    b.Property<int?>("UnidadeId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD");

                    b.HasKey("PessoaId")
                        .HasName("REQ_Pessoa_P01");

                    b.ToTable("REQ_PESSOA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Pessoas.SinPessoasReqPag.SinPessoaReqPag", b =>
                {
                    b.Property<long>("PessoaId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PESSOA");

                    b.Property<long>("CodPessoaReqPag")
                        .HasColumnType("bigint")
                        .HasColumnName("COD_CADAST_PESSOA");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOM_PESSOA");

                    b.HasKey("PessoaId", "CodPessoaReqPag")
                        .HasName("SIN_PESSOA_REQPAG_P01");

                    b.ToTable("SIN_PESSOA_REQPAG", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Planos.Plano", b =>
                {
                    b.Property<int>("Seq_Plano")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_PLANO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Plano"));

                    b.Property<int>("Ano")
                        .HasColumnType("int")
                        .HasColumnName("ANO");

                    b.Property<string>("Descricao")
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_PLANO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int>("Mes")
                        .HasColumnType("int")
                        .HasColumnName("MES");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("NOM_PLANO");

                    b.Property<int>("SEQ_UG")
                        .HasColumnType("int");

                    b.Property<int?>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.Property<string>("TipoPlano")
                        .IsRequired()
                        .HasColumnType("varchar(10)")
                        .HasColumnName("TIPO");

                    b.HasKey("Seq_Plano")
                        .HasName("CJF_PLANO_P01");

                    b.ToTable("CJF_PLANO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.PrecatoriosOrigensPCT.PrecatorioOrigemPCT", b =>
                {
                    b.Property<int>("ControleProcessamentoId")
                        .HasColumnType("int")
                        .HasColumnName("CONTROLEPROCESSAMENTOID");

                    b.Property<string>("NumeroProcesso")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUMEROPROCESSO");

                    b.Property<string>("AcaoOriginaria")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ACAOORIGINARIA");

                    b.Property<int?>("AnoOriginal")
                        .HasColumnType("int")
                        .HasColumnName("ANOORIGINAL");

                    b.Property<int>("AnoProposta")
                        .HasColumnType("int")
                        .HasColumnName("ANOPROPOSTA");

                    b.Property<string>("CPFAdvogado")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CPFADVOGADO");

                    b.Property<int?>("CodigoAgencia")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOAGENCIA");

                    b.Property<int?>("CodigoBanco")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOBANCO");

                    b.Property<string>("CodigoRequerente")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGOREQUERENTE");

                    b.Property<string>("CodigoTUA")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGOTUA");

                    b.Property<int?>("CodigoUORCadastradora")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOUORCADASTRADORA");

                    b.Property<DateTime?>("DataAjuizamentoAcao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAAJUIZAMENTOACAO");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAALTERACAO");

                    b.Property<DateTime>("DataAutuacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAAUTUACAO");

                    b.Property<DateTime?>("DataAutuacaoOriginaria")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAAUTUACAOORIGINARIA");

                    b.Property<DateTime?>("DataBaseUltimoCalculo")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATABASEULTIMOCALCULO");

                    b.Property<DateTime>("DataExtracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAEXTRACAO");

                    b.Property<DateTime?>("DataImportacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAIMPORTACAO");

                    b.Property<DateTime?>("DataInclusao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAINCLUSAO");

                    b.Property<DateTime?>("DataTransitoJulgado")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATATRANSITOJULGADO");

                    b.Property<DateTime?>("DataTransitoJulgadoExecucao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATATRANSITOJULGADOEXECUCAO");

                    b.Property<string>("IndicadorPrecatorioEC94")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORPRECATORIOEC94");

                    b.Property<string>("IndicadorPrecatorioReinclusao13463")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORPRECATORIOREINCLUSAO13463");

                    b.Property<string>("IndicadorPrecatorioTributario")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORPRECATORIOTRIBUTARIO");

                    b.Property<int?>("MesReferencia")
                        .HasColumnType("int")
                        .HasColumnName("MESREFERENCIA");

                    b.Property<int?>("NaturezaDespesa")
                        .HasColumnType("int")
                        .HasColumnName("NATUREZADESPESA");

                    b.Property<string>("NomeAdvogado")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEADVOGADO");

                    b.Property<string>("NomeIndiceAtualizacaoMonetariaAdotado")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEINDICEATUALIZACAOMONETARIAADOTADO");

                    b.Property<string>("NomeRequerente")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEREQUERENTE");

                    b.Property<string>("NumeroConta")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROCONTA");

                    b.Property<string>("NumeroGRU")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROGRU");

                    b.Property<string>("NumeroPrecatorioOrigem")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPRECATORIOORIGEM");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ");

                    b.Property<string>("OutraEntidade")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("OUTRAENTIDADE");

                    b.Property<long?>("ProcessoGeradoId")
                        .HasColumnType("bigint")
                        .HasColumnName("PROCESSOGERADOID");

                    b.Property<string>("TipoBloqueio")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOBLOQUEIO");

                    b.Property<string>("TipoCausa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOCAUSA");

                    b.Property<int?>("TipoDespesa")
                        .HasColumnType("int")
                        .HasColumnName("TIPODESPESA");

                    b.Property<string>("TipoMovimento")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOMOVIMENTO");

                    b.Property<int?>("TipoSentenca")
                        .HasColumnType("int")
                        .HasColumnName("TIPOSENTENCA");

                    b.Property<int?>("TipoVaraComarca")
                        .HasColumnType("int")
                        .HasColumnName("TIPOVARACOMARCA");

                    b.Property<int?>("UnidadeExecutada")
                        .HasColumnType("int")
                        .HasColumnName("UNIDADEEXECUTADA");

                    b.Property<string>("UsuarioAlteracao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("USUARIOALTERACAO");

                    b.Property<string>("UsuarioInclusao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("USUARIOINCLUSAO");

                    b.Property<decimal?>("ValorExpedicao")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_EXPEDICAO");

                    b.Property<decimal>("ValorOriginal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_ORIGINAL");

                    b.Property<decimal?>("ValorOriginalUltimoCalculo")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_ORIGINAL_ULTIMO_CALCULO");

                    b.Property<decimal?>("ValorParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA1");

                    b.Property<decimal?>("ValorParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA10");

                    b.Property<decimal?>("ValorParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA2");

                    b.Property<decimal?>("ValorParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA3");

                    b.Property<decimal?>("ValorParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA4");

                    b.Property<decimal?>("ValorParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA5");

                    b.Property<decimal?>("ValorParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA6");

                    b.Property<decimal?>("ValorParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA7");

                    b.Property<decimal?>("ValorParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA8");

                    b.Property<decimal?>("ValorParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA9");

                    b.Property<int?>("VaraExecucao")
                        .HasColumnType("int")
                        .HasColumnName("VARAEXECUCAO");

                    b.Property<int?>("VaraOrigem")
                        .HasColumnType("int")
                        .HasColumnName("VARAORIGEM");

                    b.HasKey("ControleProcessamentoId", "NumeroProcesso");

                    b.ToTable("PCT_PRECATORIOS", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("AcaoOriginaria")
                                .HasColumnName("ACAO_ORIGINARIA");

                            t.Property("AnoOriginal")
                                .HasColumnName("ANO_ORIGINAL");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CPFAdvogado")
                                .HasColumnName("CPF_ADVOGADO");

                            t.Property("CodigoAgencia")
                                .HasColumnName("CODIGO_AGENCIA");

                            t.Property("CodigoBanco")
                                .HasColumnName("CODIGO_BANCO");

                            t.Property("CodigoRequerente")
                                .HasColumnName("CODIGO_REQUERENTE");

                            t.Property("CodigoTUA")
                                .HasColumnName("COD_TUA");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("DataAjuizamentoAcao")
                                .HasColumnName("DATA_AJUIZAMENTO_ACAO");

                            t.Property("DataAlteracao")
                                .HasColumnName("DATA_ALTERACAO");

                            t.Property("DataAutuacao")
                                .HasColumnName("DATA_AUTUACAO");

                            t.Property("DataExtracao")
                                .HasColumnName("DAT_EXTRAC");

                            t.Property("DataImportacao")
                                .HasColumnName("DAT_IMPORT");

                            t.Property("DataInclusao")
                                .HasColumnName("DATA_INCLUSAO");

                            t.Property("DataTransitoJulgado")
                                .HasColumnName("DATA_TRANSITO_JULGADO");

                            t.Property("DataTransitoJulgadoExecucao")
                                .HasColumnName("DATA_TRANSITO_JULGADO_EXECUCAO");

                            t.Property("MesReferencia")
                                .HasColumnName("MES_REF");

                            t.Property("NaturezaDespesa")
                                .HasColumnName("NATUREZA_DESPESA");

                            t.Property("NomeAdvogado")
                                .HasColumnName("NOME_ADVOGADO");

                            t.Property("NomeRequerente")
                                .HasColumnName("NOME_REQUERENTE");

                            t.Property("NumeroConta")
                                .HasColumnName("NUMERO_CONTA");

                            t.Property("OutraEntidade")
                                .HasColumnName("OUTRA_ENTIDADE");

                            t.Property("ProcessoGeradoId")
                                .HasColumnName("SEQ_PROCES");

                            t.Property("TipoBloqueio")
                                .HasColumnName("BLOQUEIO");

                            t.Property("TipoCausa")
                                .HasColumnName("TIPO_CAUSA");

                            t.Property("TipoDespesa")
                                .HasColumnName("TIPO_DESPESA");

                            t.Property("TipoMovimento")
                                .HasColumnName("TIPO_MOVIMENTO");

                            t.Property("TipoSentenca")
                                .HasColumnName("TIPO_SENTENCA");

                            t.Property("TipoVaraComarca")
                                .HasColumnName("TIPO_VARA_COMARCA");

                            t.Property("UnidadeExecutada")
                                .HasColumnName("UNIDADE_EXECUTADA");

                            t.Property("UsuarioAlteracao")
                                .HasColumnName("USUARIO_ALTERACAO");

                            t.Property("UsuarioInclusao")
                                .HasColumnName("USUARIO_INCLUSAO");

                            t.Property("VaraOrigem")
                                .HasColumnName("VARA_ORIGEM");
                        });

                    b.SplitToTable("PCT_PRECATORIOS2", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("DataAutuacaoOriginaria")
                                .HasColumnName("DATA_AUTUACAO_ORIGINARIA");

                            t.Property("DataBaseUltimoCalculo")
                                .HasColumnName("DATA_BASE_ULTIMO_CALCULO");

                            t.Property("IndicadorPrecatorioEC94")
                                .HasColumnName("IND_PRECATORIO_EC94");

                            t.Property("IndicadorPrecatorioReinclusao13463")
                                .HasColumnName("IND_REINCLUSAO_13463");

                            t.Property("IndicadorPrecatorioTributario")
                                .HasColumnName("IND_PRECATORIO_TRIBUTARIO");

                            t.Property("NomeIndiceAtualizacaoMonetariaAdotado")
                                .HasColumnName("NOME_INDICE_ATUALIZACAO_MONETARIA_ADOTADO");

                            t.Property("NumeroGRU")
                                .HasColumnName("NUMERO_GRU");

                            t.Property("NumeroPrecatorioOrigem")
                                .HasColumnName("NUMERO_PRECATORIO_ORIGEM");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ");

                            t.Property("ValorExpedicao");

                            t.Property("ValorOriginalUltimoCalculo");

                            t.Property("VaraExecucao")
                                .HasColumnName("VARA_EXECUCAO");
                        });
                });

            modelBuilder.Entity("TRF3.SISPREC.PrecatoriosOrigensRPV.PrecatorioOrigemRPV", b =>
                {
                    b.Property<int>("ControleProcessamentoId")
                        .HasColumnType("int")
                        .HasColumnName("CONTROLEPROCESSAMENTOID");

                    b.Property<string>("NumeroProcesso")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUMEROPROCESSO");

                    b.Property<string>("AcaoOriginaria")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ACAOORIGINARIA");

                    b.Property<int?>("AnoOriginal")
                        .HasColumnType("int")
                        .HasColumnName("ANOORIGINAL");

                    b.Property<int>("AnoProposta")
                        .HasColumnType("int")
                        .HasColumnName("ANOPROPOSTA");

                    b.Property<string>("CPFAdvogado")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CPFADVOGADO");

                    b.Property<int?>("CodigoAgencia")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOAGENCIA");

                    b.Property<int?>("CodigoBanco")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOBANCO");

                    b.Property<string>("CodigoRequerente")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGOREQUERENTE");

                    b.Property<string>("CodigoTUA")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("CODIGOTUA");

                    b.Property<int?>("CodigoUORCadastradora")
                        .HasColumnType("int")
                        .HasColumnName("CODIGOUORCADASTRADORA");

                    b.Property<DateTime?>("DataAjuizamentoAcao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAAJUIZAMENTOACAO");

                    b.Property<DateTime?>("DataAlteracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAALTERACAO");

                    b.Property<DateTime>("DataAutuacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAAUTUACAO");

                    b.Property<DateTime?>("DataAutuacaoOriginaria")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAAUTUACAOORIGINARIA");

                    b.Property<DateTime?>("DataBaseUltimoCalculo")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATABASEULTIMOCALCULO");

                    b.Property<DateTime>("DataExtracao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAEXTRACAO");

                    b.Property<DateTime?>("DataImportacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAIMPORTACAO");

                    b.Property<DateTime?>("DataInclusao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATAINCLUSAO");

                    b.Property<DateTime?>("DataTransitoJulgado")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATATRANSITOJULGADO");

                    b.Property<DateTime?>("DataTransitoJulgadoExecucao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATATRANSITOJULGADOEXECUCAO");

                    b.Property<string>("IndicadorPrecatorioEC94")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORPRECATORIOEC94");

                    b.Property<string>("IndicadorPrecatorioReinclusao13463")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORPRECATORIOREINCLUSAO13463");

                    b.Property<string>("IndicadorPrecatorioTributario")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("INDICADORPRECATORIOTRIBUTARIO");

                    b.Property<int?>("MesReferencia")
                        .HasColumnType("int")
                        .HasColumnName("MESREFERENCIA");

                    b.Property<int?>("NaturezaDespesa")
                        .HasColumnType("int")
                        .HasColumnName("NATUREZADESPESA");

                    b.Property<string>("NomeAdvogado")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEADVOGADO");

                    b.Property<string>("NomeIndiceAtualizacaoMonetariaAdotado")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEINDICEATUALIZACAOMONETARIAADOTADO");

                    b.Property<string>("NomeRequerente")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOMEREQUERENTE");

                    b.Property<string>("NumeroConta")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROCONTA");

                    b.Property<string>("NumeroGRU")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROGRU");

                    b.Property<string>("NumeroPrecatorioOrigem")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPRECATORIOORIGEM");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ10")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ10");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ2")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ2");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ3")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ3");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ4")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ4");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ5")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ5");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ6")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ6");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ7")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ7");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ8")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ8");

                    b.Property<string>("NumeroProcessoAnteriorPadraoCNJ9")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUMEROPROCESSOANTERIORPADRAOCNJ9");

                    b.Property<string>("OutraEntidade")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("OUTRAENTIDADE");

                    b.Property<long?>("ProcessoGeradoId")
                        .HasColumnType("bigint")
                        .HasColumnName("PROCESSOGERADOID");

                    b.Property<string>("TipoBloqueio")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOBLOQUEIO");

                    b.Property<string>("TipoCausa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOCAUSA");

                    b.Property<int?>("TipoDespesa")
                        .HasColumnType("int")
                        .HasColumnName("TIPODESPESA");

                    b.Property<string>("TipoMovimento")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPOMOVIMENTO");

                    b.Property<int?>("TipoSentenca")
                        .HasColumnType("int")
                        .HasColumnName("TIPOSENTENCA");

                    b.Property<int?>("TipoVaraComarca")
                        .HasColumnType("int")
                        .HasColumnName("TIPOVARACOMARCA");

                    b.Property<int?>("UnidadeExecutada")
                        .HasColumnType("int")
                        .HasColumnName("UNIDADEEXECUTADA");

                    b.Property<string>("UsuarioAlteracao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("USUARIOALTERACAO");

                    b.Property<string>("UsuarioInclusao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("USUARIOINCLUSAO");

                    b.Property<decimal?>("ValorExpedicao")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_EXPEDICAO");

                    b.Property<decimal>("ValorOriginal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_ORIGINAL");

                    b.Property<decimal?>("ValorOriginalUltimoCalculo")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_ORIGINAL_ULTIMO_CALCULO");

                    b.Property<decimal?>("ValorParcela1")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA1");

                    b.Property<decimal?>("ValorParcela10")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA10");

                    b.Property<decimal?>("ValorParcela2")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA2");

                    b.Property<decimal?>("ValorParcela3")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA3");

                    b.Property<decimal?>("ValorParcela4")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA4");

                    b.Property<decimal?>("ValorParcela5")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA5");

                    b.Property<decimal?>("ValorParcela6")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA6");

                    b.Property<decimal?>("ValorParcela7")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA7");

                    b.Property<decimal?>("ValorParcela8")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA8");

                    b.Property<decimal?>("ValorParcela9")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VALOR_PARCELA9");

                    b.Property<int?>("VaraExecucao")
                        .HasColumnType("int")
                        .HasColumnName("VARAEXECUCAO");

                    b.Property<int?>("VaraOrigem")
                        .HasColumnType("int")
                        .HasColumnName("VARAORIGEM");

                    b.HasKey("ControleProcessamentoId", "NumeroProcesso");

                    b.ToTable("RPV_PRECATORIOS", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("AcaoOriginaria")
                                .HasColumnName("ACAO_ORIGINARIA");

                            t.Property("AnoOriginal")
                                .HasColumnName("ANO_ORIGINAL");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CPFAdvogado")
                                .HasColumnName("CPF_ADVOGADO");

                            t.Property("CodigoAgencia")
                                .HasColumnName("CODIGO_AGENCIA");

                            t.Property("CodigoBanco")
                                .HasColumnName("CODIGO_BANCO");

                            t.Property("CodigoRequerente")
                                .HasColumnName("CODIGO_REQUERENTE");

                            t.Property("CodigoTUA")
                                .HasColumnName("COD_TUA");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("DataAjuizamentoAcao")
                                .HasColumnName("DATA_AJUIZAMENTO_ACAO");

                            t.Property("DataAlteracao")
                                .HasColumnName("DATA_ALTERACAO");

                            t.Property("DataAutuacao")
                                .HasColumnName("DATA_AUTUACAO");

                            t.Property("DataExtracao")
                                .HasColumnName("DAT_EXTRAC");

                            t.Property("DataImportacao")
                                .HasColumnName("DAT_IMPORT");

                            t.Property("DataInclusao")
                                .HasColumnName("DATA_INCLUSAO");

                            t.Property("DataTransitoJulgado")
                                .HasColumnName("DATA_TRANSITO_JULGADO");

                            t.Property("DataTransitoJulgadoExecucao")
                                .HasColumnName("DATA_TRANSITO_JULGADO_EXECUCAO");

                            t.Property("MesReferencia")
                                .HasColumnName("MES_REF");

                            t.Property("NaturezaDespesa")
                                .HasColumnName("NATUREZA_DESPESA");

                            t.Property("NomeAdvogado")
                                .HasColumnName("NOME_ADVOGADO");

                            t.Property("NomeRequerente")
                                .HasColumnName("NOME_REQUERENTE");

                            t.Property("NumeroConta")
                                .HasColumnName("NUMERO_CONTA");

                            t.Property("OutraEntidade")
                                .HasColumnName("OUTRA_ENTIDADE");

                            t.Property("ProcessoGeradoId")
                                .HasColumnName("SEQ_PROCES");

                            t.Property("TipoBloqueio")
                                .HasColumnName("BLOQUEIO");

                            t.Property("TipoCausa")
                                .HasColumnName("TIPO_CAUSA");

                            t.Property("TipoDespesa")
                                .HasColumnName("TIPO_DESPESA");

                            t.Property("TipoMovimento")
                                .HasColumnName("TIPO_MOVIMENTO");

                            t.Property("TipoSentenca")
                                .HasColumnName("TIPO_SENTENCA");

                            t.Property("TipoVaraComarca")
                                .HasColumnName("TIPO_VARA_COMARCA");

                            t.Property("UnidadeExecutada")
                                .HasColumnName("UNIDADE_EXECUTADA");

                            t.Property("UsuarioAlteracao")
                                .HasColumnName("USUARIO_ALTERACAO");

                            t.Property("UsuarioInclusao")
                                .HasColumnName("USUARIO_INCLUSAO");

                            t.Property("VaraOrigem")
                                .HasColumnName("VARA_ORIGEM");
                        });

                    b.SplitToTable("RPV_PRECATORIOS2", null, t =>
                        {
                            t.Property("ControleProcessamentoId")
                                .HasColumnName("SEQ_CONTRO_PROCES");

                            t.Property("NumeroProcesso")
                                .HasColumnName("NUMERO_PROCESSO");

                            t.Property("AnoProposta")
                                .HasColumnName("ANO_PROPOSTA");

                            t.Property("CodigoUORCadastradora")
                                .HasColumnName("CODIGO_UOR_CADASTRADORA");

                            t.Property("DataAutuacaoOriginaria")
                                .HasColumnName("DATA_AUTUACAO_ORIGINARIA");

                            t.Property("DataBaseUltimoCalculo")
                                .HasColumnName("DATA_BASE_ULTIMO_CALCULO");

                            t.Property("IndicadorPrecatorioEC94")
                                .HasColumnName("IND_PRECATORIO_EC94");

                            t.Property("IndicadorPrecatorioReinclusao13463")
                                .HasColumnName("IND_REINCLUSAO_13463");

                            t.Property("IndicadorPrecatorioTributario")
                                .HasColumnName("IND_PRECATORIO_TRIBUTARIO");

                            t.Property("NomeIndiceAtualizacaoMonetariaAdotado")
                                .HasColumnName("NOME_INDICE_ATUALIZACAO_MONETARIA_ADOTADO");

                            t.Property("NumeroGRU")
                                .HasColumnName("NUMERO_GRU");

                            t.Property("NumeroPrecatorioOrigem")
                                .HasColumnName("NUMERO_PRECATORIO_ORIGEM");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ10")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ10");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ2")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ2");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ3")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ3");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ4")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ4");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ5")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ5");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ6")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ6");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ7")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ7");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ8")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ8");

                            t.Property("NumeroProcessoAnteriorPadraoCNJ9")
                                .HasColumnName("NUMERO_PROCESSO_ANTERIOR_PADRAO_CNJ9");

                            t.Property("ValorExpedicao");

                            t.Property("ValorOriginalUltimoCalculo");

                            t.Property("VaraExecucao")
                                .HasColumnName("VARA_EXECUCAO");
                        });
                });

            modelBuilder.Entity("TRF3.SISPREC.Processos.Processo", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PROCES");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<int>("AssuntoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ASSUNT");

                    b.Property<decimal?>("BeneficiarioSomaValores")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_BENEFI_SOM_VAL");

                    b.Property<bool>("Bloqueado")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_BLOQUE");

                    b.Property<string>("CausaTipo")
                        .HasColumnType("varchar(500)")
                        .HasColumnName("DES_CAUSA_TIPO");

                    b.Property<int>("ControleProcessamentoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_PROCES");

                    b.Property<DateTime?>("DataAjuizamento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_AJUIZ");

                    b.Property<DateTime?>("DataAtualizacao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_ATUALI");

                    b.Property<DateTime?>("DataAutuacao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_AUTUA");

                    b.Property<DateTime?>("DataAutuacaoOriginaria")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_AUTUA_ORIGI");

                    b.Property<DateTime?>("DataBaseUltimoCalculo")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_BASE_ULTIM_CALC");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_CANCEL");

                    b.Property<DateTime?>("DataDeleteCJF")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_DELETE_CJF");

                    b.Property<DateTime?>("DataExclusao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_EXCLUS");

                    b.Property<DateTime?>("DataInclusao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_INCLUS");

                    b.Property<DateTime?>("DataPatchCJF")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_PATCH_CJF");

                    b.Property<DateTime?>("DataPostCJF")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_POST_CJF");

                    b.Property<DateTime?>("DataPurge")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_PURGE");

                    b.Property<DateTime?>("DataPutCJF")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_PUT_CJF");

                    b.Property<DateTime>("DataRegistro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_REGIST");

                    b.Property<DateTime?>("DataTransitoJulgado")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_TRANSI_JULGAD");

                    b.Property<DateTime?>("DataTransitoJulgadoExecucao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_TRANSI_JULGAD_EXECUC");

                    b.Property<int>("DespesaClassificacaoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CLASSI_DESPES");

                    b.Property<string>("GRUNumero")
                        .HasColumnType("varchar(100)")
                        .HasColumnName("NUM_GRU");

                    b.Property<string>("IndiceAtualizacao")
                        .HasColumnType("varchar(50)")
                        .HasColumnName("DES_INDIC");

                    b.Property<int?>("MesPrevisaoPagamento")
                        .HasColumnType("int")
                        .HasColumnName("NUM_MES_PREVIS_PAGAME");

                    b.Property<int>("MovimentoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MOVIME_TIPO");

                    b.Property<string>("Numero")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUM_PROCES");

                    b.Property<string>("NumeroOrigem")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUM_PROCES_ORIGEM");

                    b.Property<string>("NumeroPrecatorioAnterior")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUM_PRECAT_ANTERI");

                    b.Property<string>("Observacao")
                        .HasColumnType("varchar(500)")
                        .HasColumnName("DES_OBSERV");

                    b.Property<bool>("Paragrafo20Artigo1100ConstituicaoFederal")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_PARA20_ART110_CF");

                    b.Property<bool?>("ReinclusaoLei13463")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_REINCL_LEI_13453");

                    b.Property<long>("SEQ_ACAO_ORIGI")
                        .HasColumnType("bigint");

                    b.Property<long>("SEQ_CONTA_BANCAR")
                        .HasColumnType("bigint");

                    b.Property<int>("SEQ_FASE")
                        .HasColumnType("int");

                    b.Property<int>("SentencaTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SENTEN_TIPO");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.Property<int>("Seq_Unidad_Cadastradora")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_CADAST");

                    b.Property<int>("Seq_Unidad_Executada")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_EXECUT");

                    b.Property<bool>("Tributario")
                        .HasColumnType("bit")
                        .HasColumnName("IDE_TRIBUT");

                    b.Property<decimal>("ValorOriginalUltimoCalculo")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ORIGIN_ULTIM_CALC");

                    b.HasKey("Id")
                        .HasName("CJF_PROCESSO_P01");

                    b.HasIndex("Numero")
                        .HasDatabaseName("CJF_PROCESSO_i01");

                    b.ToTable("CJF_PROCESSO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ProcessosAnteriores.ProcessoAnterior", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PROCES_ANTER");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Numero")
                        .IsRequired()
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NUM_PROCES");

                    b.Property<long>("ProcessoId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PROCES");

                    b.Property<int>("Sequencial")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.Property<string>("TipoCodigo")
                        .IsRequired()
                        .HasColumnType("varchar(10)")
                        .HasColumnName("COD_PROCES_TIPO");

                    b.HasKey("Id")
                        .HasName("CJF_PROCESSO_ANTERIORES_P01");

                    b.ToTable("CJF_PROCESSO_ANTERIORES", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Propostas.Proposta", b =>
                {
                    b.Property<int>("PropostaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_PROPOS");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PropostaId"));

                    b.Property<int>("AnoProposta")
                        .HasColumnType("int")
                        .HasColumnName("ANO_PROPOS");

                    b.Property<DateTime>("DataAtualizacao")
                        .HasColumnType("date")
                        .HasColumnName("DAT_ATUALI");

                    b.Property<DateTime?>("DataInicioCalculoJuros")
                        .HasColumnType("date")
                        .HasColumnName("DAT_INICIO_CALCUL_JUROS");

                    b.Property<int>("IndicadorEconomicoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICA_ECONOM");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<int>("MesProposta")
                        .HasColumnType("int")
                        .HasColumnName("MES_PROPOS");

                    b.Property<int>("QtdMaximaParcelaAlimetar")
                        .HasColumnType("int")
                        .HasColumnName("QTD_MAXIMA_PARCEL_ALIMET");

                    b.Property<int>("QtdMaximaParcelaComum")
                        .HasColumnType("int")
                        .HasColumnName("QTD_MAXIMA_PARCEL_COMUM");

                    b.Property<int>("QtdMaximaParcelaDesapropriacao")
                        .HasColumnType("int")
                        .HasColumnName("QTD_MAXIMA_PARCEL_DESAPR");

                    b.Property<int>("QtdMaximaParcelaDesapropriacaoUnico")
                        .HasColumnType("int")
                        .HasColumnName("QTD_MAXIMA_PARCEL_DESAPR_UNICO");

                    b.Property<string>("SituacaoProposta")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_SITUAC_PROPOS");

                    b.Property<string>("TipoProcedimentoId")
                        .IsRequired()
                        .HasColumnType("char(3)")
                        .HasColumnName("COD_TIPO_PROCED");

                    b.Property<int>("UnidadeId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD");

                    b.Property<decimal>("ValorMaximo")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_MAXIMO");

                    b.Property<decimal>("ValorMinimo")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_MINIMO");

                    b.Property<decimal>("ValorMinimoParcela")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_MINIMO_PARCEL");

                    b.HasKey("PropostaId")
                        .HasName("REQ_PROPOSTA_P01");

                    b.HasIndex("UnidadeId", "TipoProcedimentoId", "AnoProposta", "MesProposta")
                        .IsUnique()
                        .HasDatabaseName("REQ_PROPOSTA_U01");

                    b.ToTable("REQ_PROPOSTA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.REquisicoesExpedientesAdministrativos.RequisicaoExpedienteAdministrativo", b =>
                {
                    b.Property<int?>("NumeroExpedienteAdministrativo")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_EXPEDI_ADMINI");

                    b.Property<string>("NumeroProtocoloRequisicao")
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<int?>("NumeroPeticaoJudicial")
                        .HasColumnType("int")
                        .HasColumnName("NUM_PETICA_JUDICI");

                    b.Property<string>("NumeroProcessoSei")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUM_PROCES_SEI");

                    b.HasKey("NumeroExpedienteAdministrativo", "NumeroProtocoloRequisicao")
                        .HasName("ANA_REQUISICAO_EXPEDIENTE_ADMINISTRATIVO_P01");

                    b.ToTable("ANA_REQUISICAO_EXPEDIENTE_ADMINISTRATIVO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoContratualParcelas.RequisicaoContratualParcela", b =>
                {
                    b.Property<long>("RequisicaoContratualParcelaId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE_CONTRA");

                    b.Property<int>("NumeroParcelaId")
                        .HasColumnType("int")
                        .HasColumnName("NUM_PARCEL");

                    b.Property<DateTime>("DataCalculoParcelaId")
                        .HasColumnType("DATETIME")
                        .HasColumnName("DAT_CALCUL_PARCEL");

                    b.Property<DateTime>("DataPrevisaoPagamentoParcela")
                        .HasColumnType("DATETIME")
                        .HasColumnName("DAT_PREVIS_PAGTO_PARCEL");

                    b.Property<decimal>("ValorAtualizadoSaldoRequerente")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_REQUER");

                    b.Property<decimal?>("ValorAtualizadoSaldoRequerenteJ")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_REQUER_J");

                    b.Property<decimal?>("ValorAtualizadoSaldoRequerenteP")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_REQUER_P");

                    b.Property<decimal>("ValorAtualizadoSaldoTotal")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_TOTAL");

                    b.Property<decimal?>("ValorAtualizadoSaldoTotalJ")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_TOTAL_J");

                    b.Property<decimal?>("ValorAtualizadoSaldoTotalP")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_TOTAL_P");

                    b.Property<decimal?>("ValorJurosMora")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_JUROS_MORA");

                    b.Property<decimal>("ValorParcelaAtualizadaRequerente")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_PARCEL_ATUALI_REQUER");

                    b.Property<decimal?>("ValorParcelaAtualizadoRequerenteJ")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_PARCEL_ATUALI_REQUER_J");

                    b.Property<decimal?>("ValorParcelaAtualizadoRequerenteP")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_PARCEL_ATUALI_REQUER_P");

                    b.HasKey("RequisicaoContratualParcelaId", "NumeroParcelaId", "DataCalculoParcelaId")
                        .HasName("REQ_REQUISICAO_CONTRATUAL_PARCELA_P01");

                    b.ToTable("REQ_REQUISICAO_CONTRATUAL_PARCELA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoEstornos.RequisicaoEstorno", b =>
                {
                    b.Property<string>("NumeroProtocoloRequisicaoId")
                        .HasColumnType("CHAR(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<string>("CodigoBeneficiario")
                        .HasColumnType("VARCHAR(14)")
                        .HasColumnName("COD_BENEFI");

                    b.Property<DateTime>("DataHoraProtocoloRequisicao")
                        .HasColumnType("DATETIME")
                        .HasColumnName("DAT_HORA_PROTOC_REQUIS");

                    b.Property<DateTime>("DataRecolhimentoConta")
                        .HasColumnType("DATETIME")
                        .HasColumnName("DAT_RECOLH_CONTA");

                    b.Property<string>("NomeBeneficiario")
                        .IsRequired()
                        .HasColumnType("VARCHAR(200)")
                        .HasColumnName("NOM_BENEFI");

                    b.Property<string>("NumeroBanco")
                        .IsRequired()
                        .HasColumnType("VARCHAR(6)")
                        .HasColumnName("NUM_BANCO");

                    b.Property<string>("NumeroContaCorrente")
                        .IsRequired()
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("NUM_CONTA_CORREN");

                    b.Property<string>("NumeroRequisicaoOriginal")
                        .IsRequired()
                        .HasColumnType("VARCHAR(20)")
                        .HasColumnName("NUM_REQUIS");

                    b.Property<decimal>("ValorRecolhimentoConta")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_RECOLH_CONTA");

                    b.HasKey("NumeroProtocoloRequisicaoId")
                        .HasName("REQ_REQUISICAO_ESTORNO_P01");

                    b.ToTable("REQ_REQUISICAO_ESTORNO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoJustificativas.RequisicaoJustificativa", b =>
                {
                    b.Property<long>("RequisicaoJustificativaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_JUSTIF");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("RequisicaoJustificativaId"));

                    b.Property<int>("AcaoJustificativaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ACAO_JUSTIF");

                    b.Property<int>("AnaliseTelaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ANALIS_TELA");

                    b.Property<string>("ComplementoMotivo")
                        .HasMaxLength(200)
                        .HasColumnType("varchar")
                        .HasColumnName("DES_JUSTIF_COMPLE");

                    b.Property<DateTime>("DataAnalise")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_ANALIS");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("NomeUsuario")
                        .IsRequired()
                        .HasColumnType("VARCHAR(50)")
                        .HasColumnName("NOM_USUARI");

                    b.Property<string>("NumeroProtocoloRequisicao")
                        .IsRequired()
                        .HasColumnType("CHAR(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<string>("Observacoes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_OBSERV");

                    b.HasKey("RequisicaoJustificativaId")
                        .HasName("ANA_REQUISICAO_JUSTIFICATIVA_P01");

                    b.ToTable("ANA_REQUISICAO_JUSTIFICATIVA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoObservacoes.RequisicaoObservacao", b =>
                {
                    b.Property<string>("NumeroProtocoloRequisicao")
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<string>("Observacao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_OBSERV");

                    b.HasKey("NumeroProtocoloRequisicao")
                        .HasName("REQ_REQUISICAO_OBSERVACAO_P01");

                    b.ToTable("REQ_REQUISICAO_OBSERVACAO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoRequerenteCompensados.RequisicaoRequerenteCompensado", b =>
                {
                    b.Property<long>("RequisicaoRequerenteCompensadoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_REQUER_COMPEN");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("RequisicaoRequerenteCompensadoId"));

                    b.Property<int>("CodigoReceitaFederalId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CODIGO_RECEIT_FEDERA");

                    b.Property<string>("IdentificacaoDebito")
                        .HasMaxLength(17)
                        .HasColumnType("varchar")
                        .HasColumnName("NUM_IDENTF_DEBITO");

                    b.Property<long>("RequisicaoParteRequerenteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE_REQUER");

                    b.Property<string>("TipoIdentificacaoDebito")
                        .HasMaxLength(3)
                        .HasColumnType("varchar")
                        .HasColumnName("IDE_TIPO_IDENTF_DEBITO");

                    b.Property<decimal>("ValorCompensado")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_COMPEN");

                    b.Property<decimal>("ValorCompensadoAtualizado")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_COMPEN_ATUALI");

                    b.HasKey("RequisicaoRequerenteCompensadoId")
                        .HasName("REQ_REQUISICAO_REQUERENTE_COMPENSADO_P01");

                    b.ToTable("REQ_REQUISICAO_REQUERENTE_COMPENSADO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoRequerenteParcelas.RequisicaoRequerenteParcela", b =>
                {
                    b.Property<long>("RequisicaoParteRequerenteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE_REQUER");

                    b.Property<int>("NumeroParcelaId")
                        .HasColumnType("int")
                        .HasColumnName("NUM_PARCEL");

                    b.Property<DateTime>("DataCalculoParcelaId")
                        .HasColumnType("DATETIME")
                        .HasColumnName("DAT_CALCUL_PARCEL");

                    b.Property<DateTime>("DataPrevisaoPagamentoParcela")
                        .HasColumnType("DATETIME")
                        .HasColumnName("DAT_PREVIS_PAGTO_PARCEL");

                    b.Property<decimal>("ValorAtualizadoSaldoRequerente")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_REQUER");

                    b.Property<decimal?>("ValorAtualizadoSaldoRequerenteJ")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_REQUER_J");

                    b.Property<decimal?>("ValorAtualizadoSaldoRequerenteP")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_REQUER_P");

                    b.Property<decimal>("ValorAtualizadoSaldoTotal")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_TOTAL");

                    b.Property<decimal?>("ValorAtualizadoSaldoTotalJ")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_TOTAL_J");

                    b.Property<decimal?>("ValorAtualizadoSaldoTotalP")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_SALDO_TOTAL_P");

                    b.Property<decimal?>("ValorJurosMora")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_JUROS_MORA");

                    b.Property<decimal>("ValorParcelaAtualizadaRequerente")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_PARCEL_ATUALI_REQUER");

                    b.Property<decimal?>("ValorParcelaAtualizadoRequerenteJ")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_PARCEL_ATUALI_REQUER_J");

                    b.Property<decimal?>("ValorParcelaAtualizadoRequerenteP")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_PARCEL_ATUALI_REQUER_P");

                    b.HasKey("RequisicaoParteRequerenteId", "NumeroParcelaId", "DataCalculoParcelaId")
                        .HasName("REQ_REQUISICAO_REQUERENTE_PARCELA_P01");

                    b.ToTable("REQ_REQUISICAO_REQUERENTE_PARCELA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoRequerenteReferencias.RequisicaoRequerenteReferencia", b =>
                {
                    b.Property<long>("PessoaIdReferencia")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PESSOA_REFER");

                    b.Property<long>("RequisicaoParteRequerenteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE_REQUER");

                    b.Property<decimal>("ValorRequisicaoReferencia")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal")
                        .HasColumnName("VAL_REQUIS_REFER");

                    b.HasKey("PessoaIdReferencia", "RequisicaoParteRequerenteId")
                        .HasName("REQ_REQUISICAO_REQUERENTE_REFERENCIA_P01");

                    b.ToTable("REQ_REQUISICAO_REQUERENTE_REFERENCIA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesOcorrencias.RequisicaoOcorrencia", b =>
                {
                    b.Property<long>("RequisicaoOcorrenciaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_OCORRE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("RequisicaoOcorrenciaId"));

                    b.Property<int>("AcaoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ACAO_TIPO");

                    b.Property<int>("CodigoMotivoOcorrenciaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MOTIVO_OCORRE");

                    b.Property<DateTime?>("DataOcorrencia")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_REQUIS_OCORRE");

                    b.Property<string>("DescricaoObservacao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_OBSERV");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("NomeUsuario")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("NOM_USUARI");

                    b.Property<string>("NumeroProcessoOrigem")
                        .HasMaxLength(20)
                        .HasColumnType("varchar")
                        .HasColumnName("NUM_PROCES_ORIGIN");

                    b.Property<string>("NumeroProtocoloRequisicaoAnterior")
                        .HasMaxLength(15)
                        .HasColumnType("varchar")
                        .HasColumnName("NUM_PROTOC_REQUIS_ANTERI");

                    b.Property<string>("NumeroProtocoloRequisicaoId")
                        .IsRequired()
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.HasKey("RequisicaoOcorrenciaId")
                        .HasName("ANA_REQUISICAO_OCORRENCIA_P01");

                    b.ToTable("ANA_REQUISICAO_OCORRENCIA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartes.RequisicaoParte", b =>
                {
                    b.Property<long>("RequisicaoParteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("RequisicaoParteId"));

                    b.Property<int?>("AdvogadoJudicialId")
                        .HasColumnType("int")
                        .HasColumnName("COD_ADVOGA");

                    b.Property<string>("NumeroProtocoloRequisicao")
                        .IsRequired()
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<long>("PessoaId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_PESSOA");

                    b.Property<string>("SituacaoParte")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_SITUAC");

                    b.Property<string>("TipoParte")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_TIPO_PARTE");

                    b.Property<string>("TipoRepresentacaoParte")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_ADVOGA_PROCUR");

                    b.HasKey("RequisicaoParteId")
                        .HasName("REQ_REQUISICAO_PARTE_P01");

                    b.ToTable("REQ_REQUISICAO_PARTE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesContratuais.RequisicaoParteContratual", b =>
                {
                    b.Property<long>("RequisicaoParteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE");

                    b.Property<long>("RequisicaoRequerenteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE_REQUER");

                    b.Property<int?>("TipoContratual")
                        .HasMaxLength(30)
                        .HasColumnType("int")
                        .HasColumnName("TIP_REQUER_CONTRL");

                    b.Property<decimal>("ValorAtualizadoParte")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_PARTE");

                    b.Property<decimal?>("ValorAtualizadoParteJuros")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_PARTE_J");

                    b.Property<decimal?>("ValorAtualizadoPartePrincipal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_PARTE_P");

                    b.Property<decimal?>("ValorJurosMora")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_JUROS_MORA");

                    b.Property<decimal>("ValorParte")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS_PARTE");

                    b.Property<decimal?>("ValorParteJuros")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS_PARTE_J");

                    b.Property<decimal?>("ValorPartePrincipal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS_PARTE_P");

                    b.HasKey("RequisicaoParteId")
                        .HasName("REQ_REQUISICAO_PARTE_CONTRATUAL_P01");

                    b.ToTable("REQ_REQUISICAO_PARTE_CONTRATUAL", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", b =>
                {
                    b.Property<long>("RequisicaoParteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE_REQUER");

                    b.Property<DateTime?>("DataNascimentoPessoaFisica")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_NASCIM_PESSOA_FISICA");

                    b.Property<bool?>("PossuiDeficiencia")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_DEFICI");

                    b.Property<bool?>("PossuiDoencaGrave")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_DOENCA_GRAVE");

                    b.Property<decimal>("ValorAtualizadoParte")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_PARTE");

                    b.Property<decimal?>("ValorAtualizadoParteJuros")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_PARTE_J");

                    b.Property<decimal?>("ValorAtualizadoPartePrincipal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_PARTE_P");

                    b.Property<decimal?>("ValorJurosMora")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_JUROS_MORA");

                    b.Property<decimal>("ValorParte")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS_PARTE");

                    b.Property<decimal?>("ValorParteJuros")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS_PARTE_J");

                    b.Property<decimal?>("ValorPartePrincipal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS_PARTE_P");

                    b.HasKey("RequisicaoParteId")
                        .HasName("REQ_REQUISICAO_PARTE_REQUERENTE_P01");

                    b.ToTable("REQ_REQUISICAO_PARTE_REQUERENTE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesRequerentesIR.RequisicaoParteRequerenteIr", b =>
                {
                    b.Property<long>("RequisicaoParteRequerenteIrId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_REQUER_IR");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("RequisicaoParteRequerenteIrId"));

                    b.Property<int>("AnoExerciCorren")
                        .HasColumnType("int")
                        .HasColumnName("ANO_EXERCI_CORREN");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<int>("NumMesExerciCorren")
                        .HasColumnType("int")
                        .HasColumnName("NUM_MESES_EXERCI_CORREN");

                    b.Property<int>("NumMesesExerciAnteri")
                        .HasColumnType("int")
                        .HasColumnName("NUM_MESES_EXERCI_ANTERI");

                    b.Property<long>("RequisicaoParteRequerenteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE_REQUER");

                    b.Property<decimal>("ValAtualiDeducaIndivi")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_DEDUCA_INDIVI");

                    b.Property<decimal>("ValAtualiExerciAnteri")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_EXERCI_ANTERI");

                    b.Property<decimal>("ValAtualiExerciCorren")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_EXERCI_CORREN");

                    b.Property<decimal>("ValDeducaIndivi")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_DEDUCA_INDIVI");

                    b.Property<decimal>("ValExerciAnteri")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_EXERCI_ANTERI");

                    b.Property<decimal>("ValExerciCorren")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_EXERCI_CORREN");

                    b.HasKey("RequisicaoParteRequerenteIrId")
                        .HasName("REQ_REQUISICAO_REQUERENTE_IR_P01");

                    b.ToTable("REQ_REQUISICAO_REQUERENTE_IR", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesRequerentesPSS.RequisicaoParteRequerentePss", b =>
                {
                    b.Property<long>("RequisicaoParteRequerentePssId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_REQUER_PSS");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("RequisicaoParteRequerentePssId"));

                    b.Property<string>("IdeCondicServid")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasColumnName("IDE_CONDIC_SERVID");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<long>("RequisicaoParteRequerenteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE_REQUER");

                    b.Property<int>("UnidadeOrcamentariaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD");

                    b.Property<decimal?>("ValAtualiRequisPartePss")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_PARTE_PSS");

                    b.Property<decimal?>("ValRequisPartePss")
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_REQUIS_PARTE_PSS");

                    b.HasKey("RequisicaoParteRequerentePssId")
                        .HasName("REQ_REQUISICAO_REQUERENTE_PSS_P01");

                    b.ToTable("REQ_REQUISICAO_REQUERENTE_PSS", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesReus.RequisicaoParteReu", b =>
                {
                    b.Property<long>("RequisicaoParteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE");

                    b.Property<DateTime?>("DataIntimacaoReu")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_INTIMA_REU");

                    b.HasKey("RequisicaoParteId")
                        .HasName("REQ_REQUISICAO_PARTE_REU_P01");

                    b.ToTable("REQ_REQUISICAO_PARTE_REU", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPlanosOrcamentos.RequisicaoPlanoOrcamento", b =>
                {
                    b.Property<string>("NumeroProtocoloRequisicaoId")
                        .HasColumnType("CHAR(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<DateTime>("DataPlanoOrcamentoId")
                        .HasColumnType("DATETIME")
                        .HasColumnName("DAT_PLANO_ORCAME");

                    b.Property<int>("BeneficiarioIdentificacaoTipoId")
                        .HasColumnType("INT")
                        .HasColumnName("SEQ_BENEFI_IDENTI_TIPO");

                    b.Property<int>("DespesaNaturezaId")
                        .HasColumnType("INT")
                        .HasColumnName("SEQ_NATURE_DESPES");

                    b.Property<int>("SentencaTipoId")
                        .HasColumnType("INT")
                        .HasColumnName("SEQ_SENTEN_TIPO");

                    b.Property<int>("TipoDespesaId")
                        .HasColumnType("INT")
                        .HasColumnName("SEQ_TIPO_DESPES");

                    b.Property<int>("TipoMovimentoId")
                        .HasColumnType("INT")
                        .HasColumnName("SEQ_MOVIME_TIPO");

                    b.HasKey("NumeroProtocoloRequisicaoId", "DataPlanoOrcamentoId")
                        .HasName("REQ_REQUISICAO_PLANO_ORCAMENTO_P01");

                    b.ToTable("REQ_REQUISICAO_PLANO_ORCAMENTO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesProcessosOrigens.RequisicaoProcessoOrigem", b =>
                {
                    b.Property<string>("NumeroProcessoOriginario")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("NUM_PROCES_ORIGIN");

                    b.Property<string>("NumeroProtocoloRequisicao")
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<int>("UnidadeJudicialId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI");

                    b.Property<DateTime?>("DataProtocoloProcessoOriginal")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_PROTOC_PROCES_ORIGIN");

                    b.Property<bool>("ProcessoPrincipal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_PROCES_PRINCI");

                    b.HasKey("NumeroProcessoOriginario", "NumeroProtocoloRequisicao", "UnidadeJudicialId")
                        .HasName("REQ_REQUISICAO_PROCESSO_ORIGEM_P01");

                    b.ToTable("REQ_REQUISICAO_PROCESSO_ORIGEM", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPropostaParcela.RequisicaoPropostaParcela", b =>
                {
                    b.Property<string>("NumeroProtocoloRequisicaoId")
                        .HasMaxLength(11)
                        .HasColumnType("CHAR(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<DateTime>("DataCalculoParcelaId")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CALCUL_PARCEL");

                    b.Property<DateTime>("DataIndicacaoEconomica")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_INDICA_ECONOM");

                    b.Property<int>("QuantidadeMaximaParcela")
                        .HasColumnType("int")
                        .HasColumnName("QTD_MAXIMA_PARCEL");

                    b.Property<int>("QuantidadeParcela")
                        .HasColumnType("int")
                        .HasColumnName("QTD_PARCEL");

                    b.Property<int?>("TipoIndicadorEconomicoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICA_ECONOM_TIPO");

                    b.Property<decimal?>("ValorAtualiExerciCorrente")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_EXERCI_CORREN");

                    b.Property<decimal?>("ValorAtualizadoDeducaIndivi")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_DEDUCA_INDIVI");

                    b.Property<decimal?>("ValorAtualizadoExerciAnterior")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_EXERCI_ANTERI");

                    b.Property<decimal?>("ValorAtualizadoReqPartePss")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_PARTE_PSS");

                    b.Property<decimal>("ValorAtualizadoTotal")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_TOTAL");

                    b.Property<decimal?>("ValorAtualizadoTotalJ")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_TOTAL_J");

                    b.Property<decimal?>("ValorAtualizadoTotalP")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_ATUALI_TOTAL_P");

                    b.Property<decimal>("ValorIndicadorEconomico")
                        .HasPrecision(31, 10)
                        .HasColumnType("decimal(31,10)")
                        .HasColumnName("VAL_INDICA_ECONOM");

                    b.Property<decimal?>("ValorJurosMora")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_JUROS_MORA");

                    b.Property<decimal>("ValorMinimoParcela")
                        .HasPrecision(18, 4)
                        .HasColumnType("decimal(18,4)")
                        .HasColumnName("VAL_MINIMO_PARCEL");

                    b.HasKey("NumeroProtocoloRequisicaoId", "DataCalculoParcelaId")
                        .HasName("REQ_REQUISICAO_PROPOSTA_PARCELA_P01");

                    b.ToTable("REQ_REQUISICAO_PROPOSTA_PARCELA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPropostas.RequisicaoProposta", b =>
                {
                    b.Property<string>("NumeroProtocoloRequisicao")
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<int?>("IndicadorEconomicoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_INDICA_ECONOM_TIPO");

                    b.Property<int?>("NumAgencia")
                        .HasColumnType("int")
                        .HasColumnName("NUM_AGENCI");

                    b.Property<int?>("NumBanco")
                        .HasColumnType("int")
                        .HasColumnName("NUM_BANCO");

                    b.Property<string>("NumContaCorrente")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("NUM_CONTA_CORREN");

                    b.Property<int>("PropostaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_PROPOS");

                    b.Property<bool>("PropostaInicial")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_PROPOS_INICIA");

                    b.Property<string>("SituacaoRequisicaoProposta")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_SITUAC_REQUIS_PROPOS");

                    b.HasKey("NumeroProtocoloRequisicao")
                        .HasName("REQ_REQUISICAO_PROPOSTA_P01");

                    b.ToTable("REQ_REQUISICAO_PROPOSTA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesProtocolos.AssuntosExecucoes.AssuntoExecucao", b =>
                {
                    b.Property<string>("NumeroProtocoloRequisicao")
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<int>("AssuntoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ASSUNT");

                    b.HasKey("NumeroProtocoloRequisicao", "AssuntoId")
                        .HasName("REQ_ASSUNTO_EXECUCAO_P01");

                    b.ToTable("REQ_ASSUNTO_EXECUCAO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", b =>
                {
                    b.Property<string>("NumeroProtocoloRequisicao")
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<int>("AssuntoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_ASSUNT");

                    b.Property<bool?>("BloqueioDepositoJudicial")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_BLOQUE_DEPOSI_JUDICI");

                    b.Property<DateTime?>("DataConta")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CONTA");

                    b.Property<DateTime>("DataContaLiquidacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CONTA_LIQUID");

                    b.Property<DateTime>("DataHoraProtocoloRequisicao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_HORA_PROTOC_REQUIS");

                    b.Property<DateTime?>("DataTransitoDeferimentoCompensacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_TRANSI_DEFERI_COMPEN");

                    b.Property<DateTime?>("DataTransitoJulgadoEmbargos")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_TRANSI_JULGAD_EMBARG");

                    b.Property<DateTime?>("DataTransitoJulgadoFase")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_TRANSI_JULGAD_FASE");

                    b.Property<bool>("DesapropriacaoUnicoImovel")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_DESAPR_UNICO_IMOVEL");

                    b.Property<string>("DesignacaoMagistrado")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_DESIGA_MAGIST");

                    b.Property<bool?>("ExecucaoFiscal")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_EXECUC_FISCAL");

                    b.Property<string>("IdentificadorProtocoloRequisicao")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_PROTOC_REQUIS");

                    b.Property<string>("IndicadorEstornoRequisicao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_ESTORN");

                    b.Property<string>("IndicadorInclusaoRequisicao")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_REINCL");

                    b.Property<string>("IndicadorJurosMora")
                        .HasMaxLength(3)
                        .HasColumnType("varchar")
                        .HasColumnName("IND_JUROS_MORA");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<bool?>("LevantamentoOrdemJuizo")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_LEVANT_ORDEM_JUIZO");

                    b.Property<string>("NaturezaCredito")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_NATURE_CREDIT");

                    b.Property<string>("NomeMagistrado")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NOM_MAGIST");

                    b.Property<string>("NumeroOficioRequisitorio")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUM_OFICIO_REQUIT");

                    b.Property<string>("NumeroProtocoloRequisicaoUnica")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("NUM_PROTOC_REQUIS_UNICA");

                    b.Property<bool>("RenunciaValorLimite")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_RENUNC_VALOR_LIMITE");

                    b.Property<bool?>("Selic")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_SELIC");

                    b.Property<int>("SituacaoRequisicaoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SITUAC_REQUIS");

                    b.Property<string>("StatusProtocoloRequisicao")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("STA_PROTOC_REQUIS");

                    b.Property<string>("TipoHonorario")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_REQUIS_HONORA");

                    b.Property<string>("TipoProcedimentoId")
                        .IsRequired()
                        .HasColumnType("char(3)")
                        .HasColumnName("COD_TIPO_PROCED");

                    b.Property<string>("TipoRequisicao")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("IDE_REQUIS");

                    b.Property<int?>("UnidadeJudicialId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI");

                    b.Property<decimal?>("ValorAliquotaJurosMora")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ALIQUO_JUROS_MORA");

                    b.Property<decimal>("ValorAtualizadoRequisicao")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS");

                    b.Property<decimal?>("ValorAtualizadoRequisicaoJuros")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_J");

                    b.Property<decimal?>("ValorAtualizadoRequisicaoPrincipal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_ATUALI_REQUIS_P");

                    b.Property<decimal?>("ValorCompensacao")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_COMPEN");

                    b.Property<decimal?>("ValorCompensacaoAtualizado")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_COMPEN_ATUALI");

                    b.Property<decimal?>("ValorConta")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_CONTA");

                    b.Property<decimal?>("ValorContaJuros")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_CONTA_J");

                    b.Property<decimal?>("ValorContaPrincipal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_CONTA_P");

                    b.Property<decimal?>("ValorJurosMora")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_JUROS_MORA");

                    b.Property<decimal>("ValorRequisicao")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS");

                    b.Property<decimal?>("ValorRequisicaoJuros")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS_J");

                    b.Property<decimal?>("ValorRequisicaoPrincipal")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_REQUIS_P");

                    b.Property<decimal?>("ValorTotalReferencia")
                        .HasColumnType("DECIMAL(18,4)")
                        .HasColumnName("VAL_TOTAL_REFERE");

                    b.HasKey("NumeroProtocoloRequisicao")
                        .HasName("REQ_REQUISICAO_PROTOCOLO_P01");

                    b.ToTable("REQ_REQUISICAO_PROTOCOLO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesVerificacoes.RequisicaoVerificacao", b =>
                {
                    b.Property<long>("RequisicaoVerificacaoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_VERIFI");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("RequisicaoVerificacaoId"));

                    b.Property<DateTime>("DataRequisicao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_REQUIS_VERIFI");

                    b.Property<bool>("Executado")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_EXECUT");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("NumeroProtocoloRequisicaoId")
                        .IsRequired()
                        .HasColumnType("char(11)")
                        .HasColumnName("NUM_PROTOC_REQUIS");

                    b.Property<int>("VerificacaoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_VERIFI_TIPO");

                    b.HasKey("RequisicaoVerificacaoId")
                        .HasName("ANA_REQUISICAO_VERIFICACAO_P01");

                    b.ToTable("ANA_REQUISICAO_VERIFICACAO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.SentencaTipos.SentencaTipo", b =>
                {
                    b.Property<int>("Seq_Senten_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SENTEN_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Senten_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_SENTEN_TIPO");

                    b.Property<int>("CodigoAccess")
                        .HasColumnType("int")
                        .HasColumnName("COD_SENTEN_ACCESS");

                    b.Property<string>("CodigoPCTRPV")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_SENTEN_PCTRPV_TIPO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_SENTEN_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("Sequencial")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Senten_Tipo")
                        .HasName("CJF_SENTEN_TIPO_P01");

                    b.ToTable("CJF_SENTEN_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ServidorCondicaoTipos.ServidorCondicaoTipo", b =>
                {
                    b.Property<int>("Seq_Servid_Condic_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SERVID_CONDIC_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Servid_Condic_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_SERVID_CONDIC_TIPO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_SERVID_CONDIC_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.HasKey("Seq_Servid_Condic_Tipo")
                        .HasName("CJF_SERVID_CONDIC_TIPO_P01");

                    b.ToTable("CJF_SERVID_CONDIC_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Setores.Setor", b =>
                {
                    b.Property<int>("SetorId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SETOR");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SetorId"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar")
                        .HasColumnName("NOM_SETOR");

                    b.Property<string>("Sigla")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("varchar")
                        .HasColumnName("SIG_SETOR");

                    b.HasKey("SetorId")
                        .HasName("TRF_SETOR_P01");

                    b.ToTable("TRF_SETOR", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.SincronizacaoProgressos.SincronizacaoProgresso", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SINCRO_PROGRE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("Alterado")
                        .HasColumnType("int")
                        .HasColumnName("NUM_ALTERA");

                    b.Property<string>("Entidade")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("NOM_ENTIDA");

                    b.Property<int>("Excluido")
                        .HasColumnType("int")
                        .HasColumnName("NUM_EXCLUI");

                    b.Property<int>("IdSincronizacaoDominio")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SINCRO_DOMINI");

                    b.Property<int>("Incluido")
                        .HasColumnType("int")
                        .HasColumnName("NUM_INCLUI");

                    b.Property<int>("SemAlteracao")
                        .HasColumnType("int")
                        .HasColumnName("NUM_SEM_ALTERA");

                    b.Property<int>("Total")
                        .HasColumnType("int")
                        .HasColumnName("NUM_TOTAL");

                    b.HasKey("Id")
                        .HasName("APP_SINCRONIZACAO_PROGRESSO_P01");

                    b.ToTable("APP_SINCRONIZACAO_PROGRESSO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.SincronizacoesDominios.SincronizacaoDominio", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SINCRO_DOMINI");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("DataConclusao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_CONCLU");

                    b.Property<DateTime>("DataInclusao")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_INCLUS");

                    b.Property<string>("Mensagem")
                        .IsRequired()
                        .HasColumnType("varchar(300)")
                        .HasColumnName("TXT_MENSAG");

                    b.Property<string>("Sistema")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("TIP_SISTEM");

                    b.HasKey("Id")
                        .HasName("APP_SINCRONIZACAO_DOMINIO_P01");

                    b.ToTable("APP_SINCRONIZACAO_DOMINIO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.SituacoesRequisicoesProtocolos.SituacaoRequisicaoProtocolo", b =>
                {
                    b.Property<int>("SituacaoRequisicaoProtocoloId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_SITUAC_REQUIS");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SituacaoRequisicaoProtocoloId"));

                    b.Property<string>("DescricaoSituacao")
                        .IsRequired()
                        .HasColumnType("varchar(50)")
                        .HasColumnName("DES_SITUAC_PROTOC");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.HasKey("SituacaoRequisicaoProtocoloId")
                        .HasName("REQ_SITUACAO_REQUISICAO_PROTOCOLO_P01");

                    b.ToTable("REQ_SITUACAO_REQUISICAO_PROTOCOLO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.TiposEtapaProcessamentos.TipoEtapaProcessamento", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_TIPO_ETAPA");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_CADAST");

                    b.Property<string>("Descricao")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("DES_OBSERV_TIPO_ETAPA");

                    b.Property<string>("Etapa")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)")
                        .HasColumnName("DES_TIPO_ETAPA");

                    b.HasKey("Id")
                        .HasName("APP_TIPO_ETAPA_P01");

                    b.ToTable("APP_TIPO_ETAPA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.TiposProcedimentos.TipoProcedimento", b =>
                {
                    b.Property<string>("TipoProcedimentoId")
                        .HasColumnType("char(3)")
                        .HasColumnName("COD_TIPO_PROCED");

                    b.Property<int>("BeneficiarioIdentificacaoTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_BENEFI_IDENTI_TIPO");

                    b.Property<string>("DescricaoTipoProcedimento")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_TIPO_PROCED");

                    b.Property<int>("MovimeTipoId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_MOVIME_TIPO");

                    b.HasKey("TipoProcedimentoId")
                        .HasName("REQ_TIPO_PROCEDIMENTO_P01");

                    b.ToTable("REQ_TIPO_PROCEDIMENTO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UFs.UF", b =>
                {
                    b.Property<string>("UfId")
                        .HasColumnType("varchar(2)")
                        .HasColumnName("SIG_UF");

                    b.Property<string>("NomeUf")
                        .IsRequired()
                        .HasColumnType("varchar(30)")
                        .HasColumnName("NOM_UF");

                    b.HasKey("UfId")
                        .HasName("TRF_UF_P01");

                    b.ToTable("TRF_UF", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadeJudicialTipoNaturezas.UnidadeJudicialTipoNatureza", b =>
                {
                    b.Property<int>("UnidadeJudicialTipoNaturezaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI_TIPO_NATURE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UnidadeJudicialTipoNaturezaId"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)")
                        .HasColumnName("COD_UNIDAD_JUDICI_TIPO_NATURE");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("DES_UNIDAD_JUDICI_TIPO_NATURE");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<string>("Natureza")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("TIP_NATURE_UNIDAD_JUDICI_TIPO_NATURE");

                    b.Property<int?>("SeqCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("UnidadeJudicialTipoNaturezaId")
                        .HasName("CJF_UNIDADE_JUDICI_TIPO_NATURE_P01");

                    b.HasIndex("Codigo")
                        .HasDatabaseName("CJF_UNIDADE_JUDICI_TIPO_NATURE_i01");

                    b.ToTable("CJF_UNIDADE_JUDICI_TIPO_NATURE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadeJudicialTipos.UnidadeJudicialTipo", b =>
                {
                    b.Property<int>("Seq_Unidad_Judici_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Unidad_Judici_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_UNIDAD_JUDICI_TIPO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_UNIDAD_JUDICI_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Unidad_Judici_Tipo")
                        .HasName("CJF_UNIDADE_JUDICI_TIPO_P01");

                    b.HasIndex("Codigo")
                        .HasDatabaseName("CJF_UNIDADE_JUDICI_TIPO_i01");

                    b.ToTable("CJF_UNIDADE_JUDICI_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.Unidades.Unidade", b =>
                {
                    b.Property<int>("UnidadeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UnidadeId"));

                    b.Property<string>("CodigoTipoUnidade")
                        .IsRequired()
                        .HasColumnType("varchar(1)")
                        .HasColumnName("COD_TIPO_UNIDAD");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<string>("Nome")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)")
                        .HasColumnName("NOM_UNIDAD");

                    b.Property<string>("NumeroCnpjCpf")
                        .HasMaxLength(14)
                        .HasColumnType("varchar")
                        .HasColumnName("NUM_CNPJ_CPF");

                    b.HasKey("UnidadeId")
                        .HasName("TRF_UNIDADE_P01");

                    b.ToTable("TRF_UNIDADE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesEquivalentes.UnidadeEquivalente", b =>
                {
                    b.Property<string>("CodSiafi")
                        .HasColumnType("VARCHAR(15)")
                        .HasColumnName("COD_SIAFI");

                    b.Property<string>("CodSiafiEquivalente")
                        .HasColumnType("VARCHAR(15)")
                        .HasColumnName("COD_SIAFI_EQUIVA");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.HasKey("CodSiafi", "CodSiafiEquivalente")
                        .HasName("TRF_UNIDADES_EQUIVALENTES_P01");

                    b.ToTable("TRF_UNIDADES_EQUIVALENTES", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesGestoras.UnidadeGestora", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UG");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Abreviatura")
                        .HasColumnType("varchar(100)")
                        .HasColumnName("DES_ABREVI");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_UG");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int>("SEQ_UNIDAD")
                        .HasColumnType("int");

                    b.Property<int?>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.Property<string>("UG")
                        .HasColumnType("varchar(100)")
                        .HasColumnName("COD_UG");

                    b.HasKey("Id")
                        .HasName("TRF_UNIDADE_GESTORA_P01");

                    b.ToTable("TRF_UNIDADE_GESTORA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesJudiciais.ContasUnidadesJudiciais.ContaUnidadeJudicial", b =>
                {
                    b.Property<int>("ContaUnidadeJudicialId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI_CONTA");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ContaUnidadeJudicialId"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<int?>("NumAgencia")
                        .HasColumnType("int")
                        .HasColumnName("NUM_AGENCI");

                    b.Property<int?>("NumBanco")
                        .HasColumnType("int")
                        .HasColumnName("NUM_BANCO");

                    b.Property<string>("NumContaCorrente")
                        .IsRequired()
                        .HasColumnType("varchar(20)")
                        .HasColumnName("NUM_CC");

                    b.Property<int>("UnidadeJudicialId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI");

                    b.HasKey("ContaUnidadeJudicialId")
                        .HasName("TRF_UNIDADE_JUDICIAL_CONTA_P01");

                    b.HasIndex("NumAgencia", "NumBanco", "NumContaCorrente", "UnidadeJudicialId")
                        .IsUnique()
                        .HasDatabaseName("TRF_UNIDADE_JUDICIAL_CONTA_U01")
                        .HasFilter("[NUM_AGENCI] IS NOT NULL AND [NUM_BANCO] IS NOT NULL");

                    b.ToTable("TRF_UNIDADE_JUDICIAL_CONTA", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicial", b =>
                {
                    b.Property<int>("Seq_Unidad_Judici")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Unidad_Judici"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("CodigoSiafi")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_SIAFI_UNIDAD");

                    b.Property<DateTime?>("DataRegistro")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_REGIST");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<DateTime?>("DataUtilizacaoInicio")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_INICIO");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_UNIDAD_JUDICI");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Unidad_Judici")
                        .HasName("CJF_UNIDADE_JUDICI_P01");

                    b.HasIndex("CodigoSiafi")
                        .HasDatabaseName("CJF_UNIDADE_JUDICI_i01");

                    b.ToTable("CJF_UNIDADE_JUDICI", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicialOrigem", b =>
                {
                    b.Property<int>("UnidadeJudicialOrigemId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_JUDICI");

                    b.Property<string>("CodigoEspecicacaoTipoJuizo")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasColumnName("COD_ESPECI_TIPO_JUIZO");

                    b.Property<string>("Email")
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)")
                        .HasColumnName("DES_EMAIL");

                    b.Property<string>("TipoJuizo")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("nvarchar(1)")
                        .HasColumnName("COD_TIPO_JUIZO");

                    b.HasKey("UnidadeJudicialOrigemId")
                        .HasName("TRF_UNIDADE_JUDICIAL_ORIGEM_P01");

                    b.ToTable("TRF_UNIDADE_JUDICIAL_ORIGEM", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", b =>
                {
                    b.Property<int>("UnidadeOrcamentariaId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD");

                    b.Property<string>("Abreviatura")
                        .HasColumnType("varchar(100)")
                        .HasColumnName("COD_ABREVI");

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("CodigoSiafi")
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_SIAFI_UNIDAD");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<DateTime?>("DataUtilizacaoInicio")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_INICIO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<int?>("SequencialCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.Property<int?>("UnidadeSuperiorIdCJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UNIDAD_SUPERI_CJF");

                    b.HasKey("UnidadeOrcamentariaId")
                        .HasName("CJF_UNIDADE_P01");

                    b.HasIndex("CodigoSiafi")
                        .HasDatabaseName("CJF_UNIDADE_i01");

                    b.ToTable("CJF_UNIDADE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ValorTipos.ValorTipo", b =>
                {
                    b.Property<int>("Seq_Valor_Tipo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_VALOR_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Seq_Valor_Tipo"));

                    b.Property<bool>("Ativo")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("Codigo")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_VALOR_TIPO");

                    b.Property<string>("CodigoProcessoBeneficiario")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_PROCES_BENEFI");

                    b.Property<string>("CodigoUtilizacaoContexto")
                        .IsRequired()
                        .HasColumnType("varchar(15)")
                        .HasColumnName("COD_UTILIZ_CONTE");

                    b.Property<DateTime?>("DataUtilizacaoFim")
                        .HasColumnType("datetime")
                        .HasColumnName("DAT_UTILI_FIM");

                    b.Property<string>("Descricao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_VALOR_TIPO");

                    b.Property<bool>("FoiSincronizadoCjf")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("IDE_SINCRO_CJF");

                    b.Property<string>("Observacao")
                        .IsRequired()
                        .HasColumnType("varchar(200)")
                        .HasColumnName("DES_OBSERV_VALOR_TIPO");

                    b.Property<int?>("Seq_CJF")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CJF");

                    b.HasKey("Seq_Valor_Tipo")
                        .HasName("CJF_VALOR_TIPO_P01");

                    b.ToTable("CJF_VALOR_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.VerificacaoTipos.VerificacaoTipo", b =>
                {
                    b.Property<int>("VerificacaoTipoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_VERIFI_TIPO");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("VerificacaoTipoId"));

                    b.Property<int>("AnaliseTelaId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(2)
                        .HasColumnName("SEQ_ANALIS_TELA");

                    b.Property<bool>("Ativo")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_ATIVO");

                    b.Property<string>("DescricaoTipo")
                        .IsRequired()
                        .HasMaxLength(80)
                        .HasColumnType("nvarchar(80)")
                        .HasColumnName("DES_VERIFI_TIPO");

                    b.HasKey("VerificacaoTipoId")
                        .HasName("VERIFICACAO_TIPO_P01");

                    b.ToTable("ANA_VERIFICACAO_TIPO", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.VerificacoesCnpjCpf.VerificacaoCnpjCpf", b =>
                {
                    b.Property<long>("VerificacaoCnpjCpfId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_VERIFI_CNPJ_CPF");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("VerificacaoCnpjCpfId"));

                    b.Property<DateTime?>("DataNascimentoReceita")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_NASCIM_PESSOA_RECEIT");

                    b.Property<DateTime>("DataVerificacao")
                        .HasColumnType("datetime2")
                        .HasColumnName("DAT_VERIFI_CNPJ_CPF");

                    b.Property<string>("DescricaoErro")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DES_ERRO");

                    b.Property<bool>("Divergente")
                        .HasColumnType("bit")
                        .HasColumnName("SIN_DIVERG");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("SIN_EXCLUI");

                    b.Property<bool>("MaisRecente")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true)
                        .HasColumnName("SIN_MAIS_RECENT");

                    b.Property<string>("NomeMaePessoaReceita")
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)")
                        .HasColumnName("NOM_MAE_PESSOA_RECEIT");

                    b.Property<string>("NomePessoaReceita")
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)")
                        .HasColumnName("NOM_PESSOA_RECEIT");

                    b.Property<string>("NumeroCnpjCpf")
                        .IsRequired()
                        .HasMaxLength(14)
                        .HasColumnType("nvarchar(14)")
                        .HasColumnName("NUM_CNPJ_CPF");

                    b.Property<long>("RequisicaoParteId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_PARTE");

                    b.Property<long>("RequisicaoVerificacaoId")
                        .HasColumnType("bigint")
                        .HasColumnName("SEQ_REQUIS_VERIFI");

                    b.Property<string>("SituacaoCadastral")
                        .HasColumnType("varchar(30)")
                        .HasColumnName("IDE_SITUAC_CADAST");

                    b.HasKey("VerificacaoCnpjCpfId")
                        .HasName("ANA_VERIFICACAO_CNPJ_CPF_P01");

                    b.ToTable("ANA_VERIFICACAO_CNPJ_CPF", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ViewAuditoriaEntidades.ViewAuditoriaEntidade", b =>
                {
                    b.Property<Guid>("AuditoriaEntidadeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("AUDITORIAENTIDADEID");

                    b.Property<string>("AlteracaoLogPropriedadesExtras")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ALTERACAOLOGPROPRIEDADESEXTRAS");

                    b.Property<string>("AuditLogPropriedadesExtras")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("AUDITLOGPROPRIEDADESEXTRAS");

                    b.Property<int>("DuracaoExecucao")
                        .HasColumnType("int")
                        .HasColumnName("DURACAOEXECUCAO");

                    b.Property<string>("EnderecoIp")
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("ENDERECOIP");

                    b.Property<string>("Excecoes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EXCECOES");

                    b.Property<DateTime>("HorarioExecucao")
                        .HasColumnType("datetime2(7)")
                        .HasColumnName("HORARIOEXECUCAO");

                    b.Property<string>("MetodoHttp")
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("METODOHTTP");

                    b.Property<string>("NomeCompletoEntidade")
                        .IsRequired()
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("NOMECOMPLETOENTIDADE");

                    b.Property<int?>("StatusHttp")
                        .HasColumnType("int")
                        .HasColumnName("STATUSHTTP");

                    b.Property<string>("TipoAlteracao")
                        .IsRequired()
                        .HasColumnType("varchar(9)")
                        .HasColumnName("TIPOALTERACAO");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("URL");

                    b.Property<string>("Usuario")
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("USUARIO");

                    b.Property<string>("ValorEntidadeId")
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("VALORENTIDADEID");

                    b.HasKey("AuditoriaEntidadeId");

                    b.ToTable("VIEWAUDITORIAENTIDADES");

                    b.ToView("VW_AUDITORIAENTIDADE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ViewAuditoriaPropriedades.ViewAuditoriaPropriedade", b =>
                {
                    b.Property<Guid>("AuditoriaPropriedadeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("AUDITORIAPROPRIEDADEID");

                    b.Property<Guid>("AuditoriaEntidadeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("AUDITORIAENTIDADEID");

                    b.Property<string>("NomePropriedade")
                        .IsRequired()
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("NOMEPROPRIEDADE");

                    b.Property<string>("TipoPropriedade")
                        .IsRequired()
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("TIPOPROPRIEDADE");

                    b.Property<string>("ValorAnterior")
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("VALORANTERIOR");

                    b.Property<string>("ValorNovo")
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("VALORNOVO");

                    b.HasKey("AuditoriaPropriedadeId");

                    b.ToTable("VIEWAUDITORIAPROPRIEDADES");

                    b.ToView("VW_AUDITORIAPROPRIEDADE", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ViewControles.ViewControle", b =>
                {
                    b.Property<int>("ControleId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_PROCES");

                    b.Property<int>("FaseId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_FASE");

                    b.Property<string>("CodigoUG")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("COD_UG");

                    b.Property<DateTime?>("DataCadastro")
                        .HasColumnType("datetime")
                        .HasColumnName("DATA_CADASTRO");

                    b.Property<DateTime?>("DataCancelamento")
                        .HasColumnType("datetime")
                        .HasColumnName("DATA_EXCLUSAO");

                    b.Property<DateTime?>("DataConclusao")
                        .HasColumnType("datetime")
                        .HasColumnName("DATA_CONCLUSAO");

                    b.Property<string>("Erro")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ERRO");

                    b.Property<string>("Etapa")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ETAPA");

                    b.Property<string>("FaseAno")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FASE_ANO");

                    b.Property<string>("FaseMes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FASE_MES");

                    b.Property<string>("FaseTipo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("COD_FASE_TIPO");

                    b.Property<string>("PlanoAno")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PLANO_ANO");

                    b.Property<string>("PlanoMes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PLANO_MES");

                    b.Property<int?>("QtdeProcessos")
                        .HasColumnType("int")
                        .HasColumnName("QTDE_PROCESSOS");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("STATUS");

                    b.Property<string>("TipoPrecatorio")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPO_PRECATORIO");

                    b.HasKey("ControleId", "FaseId");

                    b.ToTable("VIEWCONTROLES");

                    b.ToView("VW_CONTROLES", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ViewFases.ViewFase", b =>
                {
                    b.Property<int>("SeqFase")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("SEQ_FASE");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SeqFase"));

                    b.Property<int>("Ano")
                        .HasColumnType("int")
                        .HasColumnName("ANO");

                    b.Property<int>("AnoPlano")
                        .HasColumnType("int")
                        .HasColumnName("ANO_PLANO");

                    b.Property<string>("CodFaseTipo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPO_FASE");

                    b.Property<string>("CodUG")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("UNIDADE");

                    b.Property<DateTime>("DataAbertura")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATA_ABERTURA");

                    b.Property<DateTime>("DataFechamento")
                        .HasColumnType("datetime2")
                        .HasColumnName("DATA_FECHAMENTO");

                    b.Property<string>("Fase")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FASE");

                    b.Property<int>("Mes")
                        .HasColumnType("int")
                        .HasColumnName("MES");

                    b.Property<int>("MesPlano")
                        .HasColumnType("int")
                        .HasColumnName("MES_PLANO");

                    b.Property<string>("Plano")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PLANO");

                    b.Property<int>("SeqDomUG")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_UG");

                    b.Property<int>("SeqPlano")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_PLANO");

                    b.Property<int>("Seq_Fase_Tipo")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_FASE_TIPO");

                    b.Property<string>("Situacao")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("SITUACAO");

                    b.Property<string>("Tipo")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPO");

                    b.HasKey("SeqFase");

                    b.ToTable("VIEWFASES");

                    b.ToView("VW_FASES", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.ViewProcessos.ViewProcesso", b =>
                {
                    b.Property<int>("ControleId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_CONTRO_PROCES");

                    b.Property<string>("NumeroProcesso")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("NUM_PROCES");

                    b.Property<int>("FaseId")
                        .HasColumnType("int")
                        .HasColumnName("SEQ_FASE");

                    b.Property<string>("CodigoUG")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("COD_UG");

                    b.Property<string>("DataAtualizacao")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("DATA_ATUALIZACAO");

                    b.Property<string>("Erro")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ERRO");

                    b.Property<string>("Etapa")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ETAPA");

                    b.Property<string>("FaseAno")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FASE_ANO");

                    b.Property<string>("FaseMes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("FASE_MES");

                    b.Property<string>("PlanoAno")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PLANO_ANO");

                    b.Property<string>("PlanoMes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("PLANO_MES");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("STATUS");

                    b.Property<string>("Tipo")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPO");

                    b.Property<string>("TipoFase")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("TIPO_FASE");

                    b.HasKey("ControleId", "NumeroProcesso", "FaseId");

                    b.ToTable("VIEWPROCESSOS");

                    b.ToView("VW_PROCESSOS", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("ApplicationName")
                        .HasMaxLength(96)
                        .HasColumnType("nvarchar(96)")
                        .HasColumnName("APPLICATIONNAME");

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("BROWSERINFO");

                    b.Property<string>("ClientId")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("CLIENTID");

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("CLIENTIPADDRESS");

                    b.Property<string>("ClientName")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("CLIENTNAME");

                    b.Property<string>("Comments")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("COMMENTS");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("CONCURRENCYSTAMP");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("CORRELATIONID");

                    b.Property<string>("Exceptions")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EXCEPTIONS");

                    b.Property<int>("ExecutionDuration")
                        .HasColumnType("int")
                        .HasColumnName("EXECUTIONDURATION");

                    b.Property<DateTime>("ExecutionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("EXECUTIONTIME");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EXTRAPROPERTIES");

                    b.Property<string>("HttpMethod")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("HTTPMETHOD");

                    b.Property<int?>("HttpStatusCode")
                        .HasColumnType("int")
                        .HasColumnName("HTTPSTATUSCODE");

                    b.Property<Guid?>("ImpersonatorTenantId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("IMPERSONATORTENANTID");

                    b.Property<string>("ImpersonatorTenantName")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("IMPERSONATORTENANTNAME");

                    b.Property<Guid?>("ImpersonatorUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("IMPERSONATORUSERID");

                    b.Property<string>("ImpersonatorUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("IMPERSONATORUSERNAME");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TENANTID");

                    b.Property<string>("TenantName")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("TENANTNAME");

                    b.Property<string>("Url")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("URL");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("USERID");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("USERNAME");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "ExecutionTime");

                    b.HasIndex("TenantId", "UserId", "ExecutionTime");

                    b.ToTable("ABPAUDITLOGS", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.AuditLogAction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<Guid>("AuditLogId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("AUDITLOGID");

                    b.Property<int>("ExecutionDuration")
                        .HasColumnType("int")
                        .HasColumnName("EXECUTIONDURATION");

                    b.Property<DateTime>("ExecutionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("EXECUTIONTIME");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EXTRAPROPERTIES");

                    b.Property<string>("MethodName")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("METHODNAME");

                    b.Property<string>("Parameters")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("PARAMETERS");

                    b.Property<string>("ServiceName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("SERVICENAME");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TENANTID");

                    b.HasKey("Id");

                    b.HasIndex("AuditLogId");

                    b.HasIndex("TenantId", "ServiceName", "MethodName", "ExecutionTime");

                    b.ToTable("ABPAUDITLOGACTIONS", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityChange", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<Guid>("AuditLogId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("AUDITLOGID");

                    b.Property<DateTime>("ChangeTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CHANGETIME");

                    b.Property<byte>("ChangeType")
                        .HasColumnType("tinyint")
                        .HasColumnName("CHANGETYPE");

                    b.Property<string>("EntityId")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("ENTITYID");

                    b.Property<Guid?>("EntityTenantId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ENTITYTENANTID");

                    b.Property<string>("EntityTypeFullName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("ENTITYTYPEFULLNAME");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EXTRAPROPERTIES");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TENANTID");

                    b.HasKey("Id");

                    b.HasIndex("AuditLogId");

                    b.HasIndex("TenantId", "EntityTypeFullName", "EntityId");

                    b.ToTable("ABPENTITYCHANGES", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityPropertyChange", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<Guid>("EntityChangeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ENTITYCHANGEID");

                    b.Property<string>("NewValue")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("NEWVALUE");

                    b.Property<string>("OriginalValue")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("ORIGINALVALUE");

                    b.Property<string>("PropertyName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("PROPERTYNAME");

                    b.Property<string>("PropertyTypeFullName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("PROPERTYTYPEFULLNAME");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TENANTID");

                    b.HasKey("Id");

                    b.HasIndex("EntityChangeId");

                    b.ToTable("ABPENTITYPROPERTYCHANGES", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.BackgroundJobs.BackgroundJobRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("CONCURRENCYSTAMP");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CREATIONTIME");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EXTRAPROPERTIES");

                    b.Property<bool>("IsAbandoned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("ISABANDONED");

                    b.Property<string>("JobArgs")
                        .IsRequired()
                        .HasMaxLength(1048576)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("JOBARGS");

                    b.Property<string>("JobName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("JOBNAME");

                    b.Property<DateTime?>("LastTryTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LASTTRYTIME");

                    b.Property<DateTime>("NextTryTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("NEXTTRYTIME");

                    b.Property<byte>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint")
                        .HasDefaultValue((byte)15)
                        .HasColumnName("PRIORITY");

                    b.Property<short>("TryCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("smallint")
                        .HasDefaultValue((short)0)
                        .HasColumnName("TRYCOUNT");

                    b.HasKey("Id");

                    b.HasIndex("IsAbandoned", "NextTryTime");

                    b.ToTable("ABPBACKGROUNDJOBS", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.SettingManagement.Setting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("NAME");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("PROVIDERKEY");

                    b.Property<string>("ProviderName")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("PROVIDERNAME");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(2048)
                        .HasColumnType("nvarchar(2048)")
                        .HasColumnName("VALUE");

                    b.HasKey("Id");

                    b.HasIndex("Name", "ProviderName", "ProviderKey")
                        .IsUnique()
                        .HasFilter("[PROVIDERNAME] IS NOT NULL AND [PROVIDERKEY] IS NOT NULL");

                    b.ToTable("ABPSETTINGS", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.SettingManagement.SettingDefinitionRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("DefaultValue")
                        .HasMaxLength(2048)
                        .HasColumnType("nvarchar(2048)")
                        .HasColumnName("DEFAULTVALUE");

                    b.Property<string>("Description")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("DESCRIPTION");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("DISPLAYNAME");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EXTRAPROPERTIES");

                    b.Property<bool>("IsEncrypted")
                        .HasColumnType("bit")
                        .HasColumnName("ISENCRYPTED");

                    b.Property<bool>("IsInherited")
                        .HasColumnType("bit")
                        .HasColumnName("ISINHERITED");

                    b.Property<bool>("IsVisibleToClients")
                        .HasColumnType("bit")
                        .HasColumnName("ISVISIBLETOCLIENTS");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("NAME");

                    b.Property<string>("Providers")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("PROVIDERS");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("ABPSETTINGDEFINITIONS", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.TenantManagement.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ID");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("CONCURRENCYSTAMP");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("CREATIONTIME");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("CREATORID");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("DELETERID");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("DELETIONTIME");

                    b.Property<int>("EntityVersion")
                        .HasColumnType("int")
                        .HasColumnName("ENTITYVERSION");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("EXTRAPROPERTIES");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("ISDELETED");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("LASTMODIFICATIONTIME");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("LASTMODIFIERID");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("NAME");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("NORMALIZEDNAME");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.HasIndex("NormalizedName");

                    b.ToTable("ABPTENANTS", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.TenantManagement.TenantConnectionString", b =>
                {
                    b.Property<Guid>("TenantId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("TENANTID");

                    b.Property<string>("Name")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("NAME");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("VALUE");

                    b.HasKey("TenantId", "Name");

                    b.ToTable("ABPTENANTCONNECTIONSTRINGS", (string)null);
                });

            modelBuilder.Entity("TRF3.SISPREC.AcoesJustificativa.AcaoJustificativa", b =>
                {
                    b.HasOne("TRF3.SISPREC.AcaoTipos.AcaoTipo", "AcaoTipo")
                        .WithMany("AcaoJustificativa")
                        .HasForeignKey("AcaoTipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("ANA_ACAO_JUSTIFICATIVA_R01");

                    b.Navigation("AcaoTipo");
                });

            modelBuilder.Entity("TRF3.SISPREC.AcoesOriginariasUnidadesJudiciais.AcaoOriginariaUnidadeJudicial", b =>
                {
                    b.HasOne("TRF3.SISPREC.AcoesOriginarias.AcaoOriginaria", "AcaoOriginaria")
                        .WithMany("UnidadesJudiciais")
                        .HasForeignKey("AcaoOriginariaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_ACAO_ORIGINARIA_UNIDADE_JUDICIAL_R01");

                    b.HasOne("TRF3.SISPREC.UnidadeJudicialTipos.UnidadeJudicialTipo", "TipoUnidadeJudicial")
                        .WithMany()
                        .HasForeignKey("TipoUnidadeJudicialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_ACAO_ORIGINARIA_UNIDADE_JUDICIAL_R03");

                    b.HasOne("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicial", "UnidadeJudicial")
                        .WithMany()
                        .HasForeignKey("UnidadeJudicialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_ACAO_ORIGINARIA_UNIDADE_JUDICIAL_R02");

                    b.HasOne("TRF3.SISPREC.UnidadeJudicialTipoNaturezas.UnidadeJudicialTipoNatureza", "UnidadeJudicialTipoNatureza")
                        .WithMany()
                        .HasForeignKey("UnidadeJudicialTipoNaturezaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_ACAO_ORIGINARIA_UNIDADE_JUDICIAL_R04");

                    b.Navigation("AcaoOriginaria");

                    b.Navigation("TipoUnidadeJudicial");

                    b.Navigation("UnidadeJudicial");

                    b.Navigation("UnidadeJudicialTipoNatureza");
                });

            modelBuilder.Entity("TRF3.SISPREC.Agencias.Agencia", b =>
                {
                    b.HasOne("TRF3.SISPREC.Bancos.Banco", "Banco")
                        .WithMany("Agencias")
                        .HasForeignKey("BancoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_AGENCIA_R01");

                    b.HasOne("TRF3.SISPREC.Municipios.Municipio", "Municipio")
                        .WithMany("Agencias")
                        .HasForeignKey("MunicipioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_AGENCIA_R02");

                    b.Navigation("Banco");

                    b.Navigation("Municipio");
                });

            modelBuilder.Entity("TRF3.SISPREC.AntecessoresBeneficiarios.AntecessorBeneficiario", b =>
                {
                    b.HasOne("TRF3.SISPREC.Beneficiarios.Beneficiario", "Beneficiario")
                        .WithMany("Antecessores")
                        .HasForeignKey("BeneficiarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_BENEFICIARIO_ANTECESSORES_R01");

                    b.Navigation("Beneficiario");
                });

            modelBuilder.Entity("TRF3.SISPREC.Assuntos.AssuntoAuxiliar", b =>
                {
                    b.HasOne("TRF3.SISPREC.Assuntos.Assunto", "Assunto")
                        .WithOne("AssuntoAuxiliar")
                        .HasForeignKey("TRF3.SISPREC.Assuntos.AssuntoAuxiliar", "AssuntoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_ASSUNTO_R01");

                    b.Navigation("Assunto");
                });

            modelBuilder.Entity("TRF3.SISPREC.Assuntos.AssuntoDespesa", b =>
                {
                    b.HasOne("TRF3.SISPREC.Assuntos.Assunto", "Assunto")
                        .WithMany("AssuntoDespesas")
                        .HasForeignKey("AssuntoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_ASSUNTO_DESPESA_R01");

                    b.HasOne("TRF3.SISPREC.DespesaClassificacoes.DespesaClassificacao", "DespesaClassificacao")
                        .WithMany()
                        .HasForeignKey("DespesaClassificacaoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_ASSUNTO_DESPESA_R02");

                    b.Navigation("Assunto");

                    b.Navigation("DespesaClassificacao");
                });

            modelBuilder.Entity("TRF3.SISPREC.Beneficiarios.Beneficiario", b =>
                {
                    b.HasOne("TRF3.SISPREC.ServidorCondicaoTipos.ServidorCondicaoTipo", "CondicaoServidorTipo")
                        .WithMany("Beneficiarios")
                        .HasForeignKey("CondicaoServidorTipoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("CJF_BENEFICIARIOS_R05");

                    b.HasOne("TRF3.SISPREC.BeneficiarioIdentificacaoTipos.BeneficiarioIdentificacaoTipo", "IdentificacaoTipo")
                        .WithMany("Beneficiarios")
                        .HasForeignKey("IdentificacaoTipoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("CJF_BENEFICIARIOS_R07");

                    b.HasOne("TRF3.SISPREC.Processos.Processo", "Processo")
                        .WithMany("Beneficiarios")
                        .HasForeignKey("ProcessoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("CJF_BENEFICIARIOS_R02");

                    b.HasOne("TRF3.SISPREC.ContaBancariaBeneficiarios.ContaBancariaBeneficiario", "ContaBancaria")
                        .WithOne("Beneficiario")
                        .HasForeignKey("TRF3.SISPREC.Beneficiarios.Beneficiario", "SEQ_CONTA_BANCAR")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("CJF_BENEFICIARIOS_R01");

                    b.HasOne("TRF3.SISPREC.BeneficiarioSucessaoTipos.BeneficiarioSucessaoTipo", "SucessaoTipo")
                        .WithMany("Beneficiarios")
                        .HasForeignKey("SucessaoTipoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("CJF_BENEFICIARIOS_R08");

                    b.HasOne("TRF3.SISPREC.BeneficiarioTipos.BeneficiarioTipo", "Tipo")
                        .WithMany("Beneficiarios")
                        .HasForeignKey("TipoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("CJF_BENEFICIARIOS_R06");

                    b.HasOne("TRF3.SISPREC.MovimentoTipos.MovimentoTipo", "TipoMovimento")
                        .WithMany("Beneficiarios")
                        .HasForeignKey("TipoMovimentoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("CJF_BENEFICIARIOS_R04");

                    b.HasOne("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", "UnidadeOrcamentaria")
                        .WithMany("Beneficiarios")
                        .HasForeignKey("UnidadeId")
                        .HasConstraintName("CJF_BENEFICIARIOS_R03");

                    b.Navigation("CondicaoServidorTipo");

                    b.Navigation("ContaBancaria");

                    b.Navigation("IdentificacaoTipo");

                    b.Navigation("Processo");

                    b.Navigation("SucessaoTipo");

                    b.Navigation("Tipo");

                    b.Navigation("TipoMovimento");

                    b.Navigation("UnidadeOrcamentaria");
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiariosOrigensPCT.BeneficiarioOrigemPCT", b =>
                {
                    b.HasOne("TRF3.SISPREC.PrecatoriosOrigensPCT.PrecatorioOrigemPCT", "PrecatorioOrigem")
                        .WithMany("BeneficiariosPCT")
                        .HasForeignKey("ControleProcessamentoId", "NumeroProcesso")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.BeneficiariosOrigensPCT.BeneficiarioOrigemPCT", null)
                        .WithOne()
                        .HasForeignKey("TRF3.SISPREC.BeneficiariosOrigensPCT.BeneficiarioOrigemPCT", "ControleProcessamentoId", "NumeroProcesso", "CodigoBeneficiario")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.PagoBeneficiariosOrigensPCT.PagoBeneficiarioOrigemPCT", "PagamentoBeneficiario")
                        .WithOne()
                        .HasForeignKey("TRF3.SISPREC.BeneficiariosOrigensPCT.BeneficiarioOrigemPCT", "ControleProcessamentoId", "NumeroProcesso", "CodigoBeneficiario")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("PagamentoBeneficiario");

                    b.Navigation("PrecatorioOrigem");
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiariosOrigensRPV.BeneficiarioOrigemRPV", b =>
                {
                    b.HasOne("TRF3.SISPREC.PrecatoriosOrigensRPV.PrecatorioOrigemRPV", "PrecatorioOrigem")
                        .WithMany("BeneficiariosRPV")
                        .HasForeignKey("ControleProcessamentoId", "NumeroProcesso")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.BeneficiariosOrigensRPV.BeneficiarioOrigemRPV", null)
                        .WithOne()
                        .HasForeignKey("TRF3.SISPREC.BeneficiariosOrigensRPV.BeneficiarioOrigemRPV", "ControleProcessamentoId", "NumeroProcesso", "CodigoBeneficiario")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.PagoBeneficiariosOrigensRPV.PagoBeneficiarioOrigemRPV", "PagamentoBeneficiario")
                        .WithOne()
                        .HasForeignKey("TRF3.SISPREC.BeneficiariosOrigensRPV.BeneficiarioOrigemRPV", "ControleProcessamentoId", "NumeroProcesso", "CodigoBeneficiario")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("PagamentoBeneficiario");

                    b.Navigation("PrecatorioOrigem");
                });

            modelBuilder.Entity("TRF3.SISPREC.CidadesRegPag.CidadeRegPag", b =>
                {
                    b.HasOne("TRF3.SISPREC.Municipios.Municipio", "Municipio")
                        .WithMany("CidadeRegPag")
                        .HasForeignKey("MunicipioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("SIN_CIDADE_REQPAG_R01");

                    b.Navigation("Municipio");
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleImportacaoRequisicaoErros.ControleImportacaoRequisicaoErro", b =>
                {
                    b.HasOne("TRF3.SISPREC.ControleImportacaoRequisicoes.ControleImportacaoRequisicao", "ControleImportacaoRequisicao")
                        .WithMany("ControleImportacaoRequisicoesErro")
                        .HasForeignKey("ControleImportacaoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("APP_CONTROLE_IMPORTACAO_REQUISICOES_ERRO_R01");

                    b.Navigation("ControleImportacaoRequisicao");
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleProcessamentoProcessos.ControleProcessamentoProcesso", b =>
                {
                    b.HasOne("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", "ControleProcessamento")
                        .WithMany("ControleProcessos")
                        .HasForeignKey("ControleProcessamentoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("APP_CONTROLE_PROCESSAMENTO_PROCESSO_R01");

                    b.Navigation("ControleProcessamento");
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", b =>
                {
                    b.HasOne("TRF3.SISPREC.ControleProcessamentoArquivos.ControleProcessamentoArquivo", "Arquivo")
                        .WithMany("Processamentos")
                        .HasForeignKey("ArquivoId")
                        .HasConstraintName("APP_CONTROLE_PROCESSAMENTO_R01");

                    b.HasOne("TRF3.SISPREC.TiposEtapaProcessamentos.TipoEtapaProcessamento", "Etapa")
                        .WithMany("Processamentos")
                        .HasForeignKey("EtapaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("APP_CONTROLE_PROCESSAMENTO_R02");

                    b.HasOne("TRF3.SISPREC.Fases.Fase", "Fase")
                        .WithMany("Processamentos")
                        .HasForeignKey("FaseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("APP_CONTROLE_PROCESSAMENTO_R03");

                    b.Navigation("Arquivo");

                    b.Navigation("Etapa");

                    b.Navigation("Fase");
                });

            modelBuilder.Entity("TRF3.SISPREC.DespesaClassificacoes.DespesaClassificacao", b =>
                {
                    b.HasOne("TRF3.SISPREC.DespesaNaturezas.DespesaNatureza", "DespesaNatureza")
                        .WithMany("DespesaClassificacoes")
                        .HasForeignKey("DespesaNaturezaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("CJF_DESPESA_CLASSIFICACAO_R01");

                    b.HasOne("TRF3.SISPREC.DespesaTipos.DespesaTipo", "DespesaTipo")
                        .WithMany("DespesaClassificacoes")
                        .HasForeignKey("DespesaTipoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("CJF_DESPESA_CLASSIFICACAO_R02");

                    b.Navigation("DespesaNatureza");

                    b.Navigation("DespesaTipo");
                });

            modelBuilder.Entity("TRF3.SISPREC.ExpedientesAdministrativos.ExpedienteAdministrativo", b =>
                {
                    b.HasOne("TRF3.SISPREC.BlocosSisprec.BlocoSisprec", "BlocoSisprec")
                        .WithMany("ExpedienteAdministrativo")
                        .HasForeignKey("BlocoSisprecId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("ANA_EXPEDIENTE_ADMINISTRATIVO_R01");

                    b.Navigation("BlocoSisprec");
                });

            modelBuilder.Entity("TRF3.SISPREC.ExpedientesAdministrativosHistorico.ExpedienteAdministrativoHistorico", b =>
                {
                    b.HasOne("TRF3.SISPREC.ExpedientesAdministrativos.ExpedienteAdministrativo", "ExpedienteAdministrativo")
                        .WithMany("ExpedienteAdministrativoHistoricos")
                        .HasForeignKey("ExpedienteAdministrativoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_EXPEDIENTE_ADMINISTRATIVO_HISTORICO_R01");

                    b.Navigation("ExpedienteAdministrativo");
                });

            modelBuilder.Entity("TRF3.SISPREC.Fases.Fase", b =>
                {
                    b.HasOne("TRF3.SISPREC.UnidadesGestoras.UnidadeGestora", "UnidadeGestora")
                        .WithMany("Fases")
                        .HasForeignKey("SEQ_UG")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("CJF_FASE_R03");

                    b.HasOne("TRF3.SISPREC.FaseTipos.FaseTipo", "FaseTipo")
                        .WithMany()
                        .HasForeignKey("Seq_Fase_Tipo")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("CJF_FASE_R02");

                    b.HasOne("TRF3.SISPREC.Planos.Plano", "Plano")
                        .WithMany("Fases")
                        .HasForeignKey("Seq_Plano")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("CJF_FASE_R01");

                    b.Navigation("FaseTipo");

                    b.Navigation("Plano");

                    b.Navigation("UnidadeGestora");
                });

            modelBuilder.Entity("TRF3.SISPREC.IndicadorEconomicos.IndicadorEconomico", b =>
                {
                    b.HasOne("TRF3.SISPREC.IndicadorEconomicoTipos.IndicadorEconomicoTipo", "TipoIndicadorEconomico")
                        .WithMany("IndicadorEconomico")
                        .HasForeignKey("IndicadorEconomicoTipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_INDICADOR_ECONOMICO_R01");

                    b.Navigation("TipoIndicadorEconomico");
                });

            modelBuilder.Entity("TRF3.SISPREC.IndiceAtualizacaoMonetarias.IndiceAtualizacaoMonetaria", b =>
                {
                    b.HasOne("TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos.IndiceAtualizacaoMonetariaTipo", "TipoIndice")
                        .WithMany("IndicesAtualizacaoMonetaria")
                        .HasForeignKey("TipoIndiceId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("CJF_INDICE_ATUALI_MONETA_R01");

                    b.Navigation("TipoIndice");
                });

            modelBuilder.Entity("TRF3.SISPREC.JustificativaComparacoes.JustificativaComparacao", b =>
                {
                    b.HasOne("TRF3.SISPREC.JustificativaDocumentos.JustificativaDocumento", "JustificativaDocumento")
                        .WithOne("JustificativaComparacao")
                        .HasForeignKey("TRF3.SISPREC.JustificativaComparacoes.JustificativaComparacao", "JustificativaDocumentoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("ANA_JUSTIFICATIVA_COMPARACAO_R02");

                    b.HasOne("TRF3.SISPREC.RequisicaoJustificativas.RequisicaoJustificativa", "RequisicaoJustificativa")
                        .WithMany("JustificativaComparacao")
                        .HasForeignKey("RequisicaoJustificativaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_JUSTIFICATIVA_COMPARACAO_R01");

                    b.Navigation("JustificativaDocumento");

                    b.Navigation("RequisicaoJustificativa");
                });

            modelBuilder.Entity("TRF3.SISPREC.JustificativaDocumentos.JustificativaDocumento", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicaoJustificativas.RequisicaoJustificativa", "RequisicaoJustificativa")
                        .WithMany("Documentos")
                        .HasForeignKey("RequisicaoJustificativaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_JUSTIFICATIVA_DOCUMENTO_R01");

                    b.Navigation("RequisicaoJustificativa");
                });

            modelBuilder.Entity("TRF3.SISPREC.LogDetalhes.LogDetalhe", b =>
                {
                    b.HasOne("TRF3.SISPREC.LogGerais.LogGeral", "Log")
                        .WithMany("LogDetalhe")
                        .HasForeignKey("LogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("APP_LOG_GERAL_DETALHES_R01");

                    b.Navigation("Log");
                });

            modelBuilder.Entity("TRF3.SISPREC.ModelosDocumentos.ModeloDocumento", b =>
                {
                    b.HasOne("TRF3.SISPREC.Setores.Setor", "Setor")
                        .WithMany("ModelosDocumentos")
                        .HasForeignKey("SetorId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_MODELO_DOCUMENTO_R01");

                    b.Navigation("Setor");
                });

            modelBuilder.Entity("TRF3.SISPREC.Municipios.Municipio", b =>
                {
                    b.HasOne("TRF3.SISPREC.UFs.UF", "UF")
                        .WithMany("Municipio")
                        .HasForeignKey("SiglaUF")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_MUNICIPIO_R01");

                    b.Navigation("UF");
                });

            modelBuilder.Entity("TRF3.SISPREC.OcorrenciaMotivos.OcorrenciaMotivo", b =>
                {
                    b.HasOne("TRF3.SISPREC.AcaoTipos.AcaoTipo", "AcaoTipo")
                        .WithMany("OcorrenciaMotivo")
                        .HasForeignKey("AcaoTipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("ANA_OCORRENCIA_MOTIVO_R01");

                    b.HasOne("TRF3.SISPREC.AnaliseTelas.AnaliseTela", "AnaliseTela")
                        .WithMany("OcorrenciaMotivo")
                        .HasForeignKey("AnaliseTelaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("ANA_OCORRENCIA_MOTIVO_R02");

                    b.Navigation("AcaoTipo");

                    b.Navigation("AnaliseTela");
                });

            modelBuilder.Entity("TRF3.SISPREC.PagoBeneficiariosOrigensPCT.PagoBeneficiarioOrigemPCT", b =>
                {
                    b.HasOne("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", "ControleProcessamentoBase")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoDadosBaseId");

                    b.HasOne("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", "ControleProcessamento")
                        .WithMany("PagosBeneficiarioOrigemPCT")
                        .HasForeignKey("ControleProcessamentoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.PrecatoriosOrigensPCT.PrecatorioOrigemPCT", "PrecatorioBase")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoDadosBaseId", "NumeroProcesso");

                    b.HasOne("TRF3.SISPREC.ControleProcessamentoProcessos.ControleProcessamentoProcesso", "ControleProcessamentoProcesso")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoId", "NumeroProcesso");

                    b.HasOne("TRF3.SISPREC.BeneficiariosOrigensPCT.BeneficiarioOrigemPCT", "BeneficiarioBase")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoDadosBaseId", "NumeroProcesso", "CodigoBeneficiario");

                    b.Navigation("BeneficiarioBase");

                    b.Navigation("ControleProcessamento");

                    b.Navigation("ControleProcessamentoBase");

                    b.Navigation("ControleProcessamentoProcesso");

                    b.Navigation("PrecatorioBase");
                });

            modelBuilder.Entity("TRF3.SISPREC.PagoBeneficiariosOrigensRPV.PagoBeneficiarioOrigemRPV", b =>
                {
                    b.HasOne("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", "ControleProcessamentoBase")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoDadosBaseId");

                    b.HasOne("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", "ControleProcessamento")
                        .WithMany("PagosBeneficiarioOrigemRPV")
                        .HasForeignKey("ControleProcessamentoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.PrecatoriosOrigensRPV.PrecatorioOrigemRPV", "PrecatorioBase")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoDadosBaseId", "NumeroProcesso");

                    b.HasOne("TRF3.SISPREC.ControleProcessamentoProcessos.ControleProcessamentoProcesso", "ControleProcessamentoProcesso")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoId", "NumeroProcesso");

                    b.HasOne("TRF3.SISPREC.BeneficiariosOrigensRPV.BeneficiarioOrigemRPV", "BeneficiarioBase")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoDadosBaseId", "NumeroProcesso", "CodigoBeneficiario");

                    b.Navigation("BeneficiarioBase");

                    b.Navigation("ControleProcessamento");

                    b.Navigation("ControleProcessamentoBase");

                    b.Navigation("ControleProcessamentoProcesso");

                    b.Navigation("PrecatorioBase");
                });

            modelBuilder.Entity("TRF3.SISPREC.Parcelas.Parcela", b =>
                {
                    b.HasOne("TRF3.SISPREC.Beneficiarios.Beneficiario", "Beneficiario")
                        .WithMany("Parcelas")
                        .HasForeignKey("BeneficiarioId");

                    b.HasOne("TRF3.SISPREC.OrdemPagamento107aTipos.OrdemPagamento107aTipo", "OrdemPagamento107aTipo")
                        .WithMany("Parcelas")
                        .HasForeignKey("OrdemPagamento107aTipoId");

                    b.HasOne("TRF3.SISPREC.Processos.Processo", "Processo")
                        .WithMany("Parcelas")
                        .HasForeignKey("ProcessoId");

                    b.HasOne("TRF3.SISPREC.ValorTipos.ValorTipo", "Tipo")
                        .WithMany("Parcelas")
                        .HasForeignKey("TipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Beneficiario");

                    b.Navigation("OrdemPagamento107aTipo");

                    b.Navigation("Processo");

                    b.Navigation("Tipo");
                });

            modelBuilder.Entity("TRF3.SISPREC.Partes.Parte", b =>
                {
                    b.HasOne("TRF3.SISPREC.Beneficiarios.Beneficiario", "Beneficiario")
                        .WithMany("Partes")
                        .HasForeignKey("BeneficiarioId")
                        .HasConstraintName("CJF_PARTES_R02");

                    b.HasOne("TRF3.SISPREC.Processos.Processo", "Processo")
                        .WithMany("Partes")
                        .HasForeignKey("ProcessoId")
                        .HasConstraintName("CJF_PARTES_R01");

                    b.Navigation("Beneficiario");

                    b.Navigation("Processo");
                });

            modelBuilder.Entity("TRF3.SISPREC.Peritos.Perito", b =>
                {
                    b.HasOne("TRF3.SISPREC.VerificacaoTipos.VerificacaoTipo", "VerificacaoTipo")
                        .WithMany()
                        .HasForeignKey("VerificacaoTipoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("TRF_LISTA_PERITO_R01");

                    b.Navigation("VerificacaoTipo");
                });

            modelBuilder.Entity("TRF3.SISPREC.Pessoas.EnderecosPessoas.EnderecoPessoa", b =>
                {
                    b.HasOne("TRF3.SISPREC.Municipios.Municipio", "Municipio")
                        .WithMany("EnderecoPessoa")
                        .HasForeignKey("MunicipioId")
                        .HasConstraintName("REQ_PESSOA_ENDERECO_R02");

                    b.HasOne("TRF3.SISPREC.Pessoas.Pessoa", "Pessoa")
                        .WithOne("Endereco")
                        .HasForeignKey("TRF3.SISPREC.Pessoas.EnderecosPessoas.EnderecoPessoa", "PessoaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_PESSOA_R01");

                    b.Navigation("Municipio");

                    b.Navigation("Pessoa");
                });

            modelBuilder.Entity("TRF3.SISPREC.Pessoas.Pessoa", b =>
                {
                    b.HasOne("TRF3.SISPREC.Unidades.Unidade", "Unidade")
                        .WithMany("Pessoa")
                        .HasForeignKey("UnidadeId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("REQ_PESSOA_R01");

                    b.Navigation("Unidade");
                });

            modelBuilder.Entity("TRF3.SISPREC.Pessoas.SinPessoasReqPag.SinPessoaReqPag", b =>
                {
                    b.HasOne("TRF3.SISPREC.Pessoas.Pessoa", "Pessoa")
                        .WithMany("SinPessoasReqPag")
                        .HasForeignKey("PessoaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("SIN_PESSOA_REQPAG_R01");

                    b.Navigation("Pessoa");
                });

            modelBuilder.Entity("TRF3.SISPREC.Planos.Plano", b =>
                {
                    b.HasOne("TRF3.SISPREC.UnidadesGestoras.UnidadeGestora", "UnidadeGestora")
                        .WithMany("Planos")
                        .HasForeignKey("SEQ_UG")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_PLANO_R01");

                    b.Navigation("UnidadeGestora");
                });

            modelBuilder.Entity("TRF3.SISPREC.PrecatoriosOrigensPCT.PrecatorioOrigemPCT", b =>
                {
                    b.HasOne("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", "ControleProcessamento")
                        .WithMany("PrecatoriosOrigemPCT")
                        .HasForeignKey("ControleProcessamentoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.Processos.Processo", "ProcessoGerado")
                        .WithMany()
                        .HasForeignKey("ProcessoGeradoId");

                    b.HasOne("TRF3.SISPREC.ControleProcessamentoProcessos.ControleProcessamentoProcesso", "ControleProcessamentoProcesso")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoId", "NumeroProcesso");

                    b.HasOne("TRF3.SISPREC.PrecatoriosOrigensPCT.PrecatorioOrigemPCT", null)
                        .WithOne()
                        .HasForeignKey("TRF3.SISPREC.PrecatoriosOrigensPCT.PrecatorioOrigemPCT", "ControleProcessamentoId", "NumeroProcesso")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ControleProcessamento");

                    b.Navigation("ControleProcessamentoProcesso");

                    b.Navigation("ProcessoGerado");
                });

            modelBuilder.Entity("TRF3.SISPREC.PrecatoriosOrigensRPV.PrecatorioOrigemRPV", b =>
                {
                    b.HasOne("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", "ControleProcessamento")
                        .WithMany("PrecatoriosOrigemRPV")
                        .HasForeignKey("ControleProcessamentoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.Processos.Processo", "ProcessoGerado")
                        .WithMany()
                        .HasForeignKey("ProcessoGeradoId");

                    b.HasOne("TRF3.SISPREC.ControleProcessamentoProcessos.ControleProcessamentoProcesso", "ControleProcessamentoProcesso")
                        .WithMany()
                        .HasForeignKey("ControleProcessamentoId", "NumeroProcesso");

                    b.HasOne("TRF3.SISPREC.PrecatoriosOrigensRPV.PrecatorioOrigemRPV", null)
                        .WithOne()
                        .HasForeignKey("TRF3.SISPREC.PrecatoriosOrigensRPV.PrecatorioOrigemRPV", "ControleProcessamentoId", "NumeroProcesso")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ControleProcessamento");

                    b.Navigation("ControleProcessamentoProcesso");

                    b.Navigation("ProcessoGerado");
                });

            modelBuilder.Entity("TRF3.SISPREC.Processos.Processo", b =>
                {
                    b.HasOne("TRF3.SISPREC.Assuntos.Assunto", "Assunto")
                        .WithMany("Processos")
                        .HasForeignKey("AssuntoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R05");

                    b.HasOne("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", "ControleProcessamento")
                        .WithMany("Processos")
                        .HasForeignKey("ControleProcessamentoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R01");

                    b.HasOne("TRF3.SISPREC.DespesaClassificacoes.DespesaClassificacao", "DespesaClassificacao")
                        .WithMany("Processos")
                        .HasForeignKey("DespesaClassificacaoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R06");

                    b.HasOne("TRF3.SISPREC.MovimentoTipos.MovimentoTipo", "MovimentoTipo")
                        .WithMany("Processos")
                        .HasForeignKey("MovimentoTipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R07");

                    b.HasOne("TRF3.SISPREC.AcoesOriginarias.AcaoOriginaria", "AcaoOriginaria")
                        .WithOne("Processo")
                        .HasForeignKey("TRF3.SISPREC.Processos.Processo", "SEQ_ACAO_ORIGI")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R03");

                    b.HasOne("TRF3.SISPREC.ContaBancarias.ContaBancaria", "ContaBancaria")
                        .WithOne("Processo")
                        .HasForeignKey("TRF3.SISPREC.Processos.Processo", "SEQ_CONTA_BANCAR")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R04");

                    b.HasOne("TRF3.SISPREC.Fases.Fase", "Fase")
                        .WithMany("Processos")
                        .HasForeignKey("SEQ_FASE")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TRF3.SISPREC.SentencaTipos.SentencaTipo", "SentencaTipo")
                        .WithMany("Processos")
                        .HasForeignKey("SentencaTipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R08");

                    b.HasOne("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", "UnidadeCadastradora")
                        .WithMany("ProcessosCadastrados")
                        .HasForeignKey("Seq_Unidad_Cadastradora")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R09");

                    b.HasOne("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", "UnidadeExecutada")
                        .WithMany("ProcessosExecutados")
                        .HasForeignKey("Seq_Unidad_Executada")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_R10");

                    b.HasOne("TRF3.SISPREC.ControleProcessamentoProcessos.ControleProcessamentoProcesso", "ControleProcessamentoProcesso")
                        .WithOne()
                        .HasForeignKey("TRF3.SISPREC.Processos.Processo", "ControleProcessamentoId", "Numero")
                        .HasConstraintName("CJF_PROCESSO_R02");

                    b.Navigation("AcaoOriginaria");

                    b.Navigation("Assunto");

                    b.Navigation("ContaBancaria");

                    b.Navigation("ControleProcessamento");

                    b.Navigation("ControleProcessamentoProcesso");

                    b.Navigation("DespesaClassificacao");

                    b.Navigation("Fase");

                    b.Navigation("MovimentoTipo");

                    b.Navigation("SentencaTipo");

                    b.Navigation("UnidadeCadastradora");

                    b.Navigation("UnidadeExecutada");
                });

            modelBuilder.Entity("TRF3.SISPREC.ProcessosAnteriores.ProcessoAnterior", b =>
                {
                    b.HasOne("TRF3.SISPREC.Processos.Processo", "Processo")
                        .WithMany("ProcessosAnteriores")
                        .HasForeignKey("ProcessoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("CJF_PROCESSO_ANTERIORES_R01");

                    b.Navigation("Processo");
                });

            modelBuilder.Entity("TRF3.SISPREC.Propostas.Proposta", b =>
                {
                    b.HasOne("TRF3.SISPREC.IndicadorEconomicos.IndicadorEconomico", "IndicadorEconomico")
                        .WithMany("Proposta")
                        .HasForeignKey("IndicadorEconomicoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_PROPOSTA_R03");

                    b.HasOne("TRF3.SISPREC.TiposProcedimentos.TipoProcedimento", "TipoProcedimento")
                        .WithMany()
                        .HasForeignKey("TipoProcedimentoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_PROPOSTA_R02");

                    b.HasOne("TRF3.SISPREC.Unidades.Unidade", "Unidade")
                        .WithMany("Propostas")
                        .HasForeignKey("UnidadeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_PROPOSTA_R01");

                    b.Navigation("IndicadorEconomico");

                    b.Navigation("TipoProcedimento");

                    b.Navigation("Unidade");
                });

            modelBuilder.Entity("TRF3.SISPREC.REquisicoesExpedientesAdministrativos.RequisicaoExpedienteAdministrativo", b =>
                {
                    b.HasOne("TRF3.SISPREC.ExpedientesAdministrativos.ExpedienteAdministrativo", "ExpedienteAdministrativo")
                        .WithMany("RequisicaoExpedienteAdministrativo")
                        .HasForeignKey("NumeroExpedienteAdministrativo")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_EXPEDIENTE_ADMINISTRATIVO_R02");

                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithMany("RequisicaoExpedienteAdministrativo")
                        .HasForeignKey("NumeroProtocoloRequisicao")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_EXPEDIENTE_ADMINISTRATIVO_R01");

                    b.Navigation("ExpedienteAdministrativo");

                    b.Navigation("RequisicaoProtocolo");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoContratualParcelas.RequisicaoContratualParcela", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPartesContratuais.RequisicaoParteContratual", "RequisicaoParteContratual")
                        .WithMany("RequisicaoContratualParcela")
                        .HasForeignKey("RequisicaoContratualParcelaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_CONTRATUAL_PARCELA_R01");

                    b.Navigation("RequisicaoParteContratual");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoEstornos.RequisicaoEstorno", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithOne("RequisicaoEstorno")
                        .HasForeignKey("TRF3.SISPREC.RequisicaoEstornos.RequisicaoEstorno", "NumeroProtocoloRequisicaoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_ESTORNO_R01");

                    b.Navigation("RequisicaoProtocolo");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoJustificativas.RequisicaoJustificativa", b =>
                {
                    b.HasOne("TRF3.SISPREC.AcoesJustificativa.AcaoJustificativa", "AcaoJustificativa")
                        .WithMany("RequisicaoJustificativa")
                        .HasForeignKey("AcaoJustificativaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_JUSTIFICATIVA_R03");

                    b.HasOne("TRF3.SISPREC.AnaliseTelas.AnaliseTela", "AnaliseTela")
                        .WithMany("RequisicaoJustificativa")
                        .HasForeignKey("AnaliseTelaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_JUSTIFICATIVA_R02");

                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithMany("RequisicaoJustificativas")
                        .HasForeignKey("NumeroProtocoloRequisicao")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_JUSTIFICATIVA_R01");

                    b.Navigation("AcaoJustificativa");

                    b.Navigation("AnaliseTela");

                    b.Navigation("RequisicaoProtocolo");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoObservacoes.RequisicaoObservacao", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithOne("RequisicaoObservacao")
                        .HasForeignKey("TRF3.SISPREC.RequisicaoObservacoes.RequisicaoObservacao", "NumeroProtocoloRequisicao")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_OBSERVACAO_R01");

                    b.Navigation("RequisicaoProtocolo");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoRequerenteCompensados.RequisicaoRequerenteCompensado", b =>
                {
                    b.HasOne("TRF3.SISPREC.CodigosReceitaFederal.CodigoReceitaFederal", "CodigoReceitaFederal")
                        .WithMany("RequisicaoParteRequerenteCompensado")
                        .HasForeignKey("CodigoReceitaFederalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_REQUERENTE_COMPENSADO_R01");

                    b.HasOne("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", "RequisicaoParteRequerente")
                        .WithMany("RequisicaoRequerenteCompensado")
                        .HasForeignKey("RequisicaoParteRequerenteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_REQUERENTE_COMPENSADO_R02");

                    b.Navigation("CodigoReceitaFederal");

                    b.Navigation("RequisicaoParteRequerente");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoRequerenteParcelas.RequisicaoRequerenteParcela", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", "RequisicaoParteRequente")
                        .WithMany("RequisicaoRequerenteParcela")
                        .HasForeignKey("RequisicaoParteRequerenteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_REQUERENTE_PARCELA_R01");

                    b.Navigation("RequisicaoParteRequente");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoRequerenteReferencias.RequisicaoRequerenteReferencia", b =>
                {
                    b.HasOne("TRF3.SISPREC.Pessoas.Pessoa", "PessoaReferencia")
                        .WithMany("RequisicaoRequerenteReferencias")
                        .HasForeignKey("PessoaIdReferencia")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_REQUERENTE_REFERENCIA_R02");

                    b.HasOne("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", "RequisicaoParteRequerente")
                        .WithMany("RequisicaoRequerenteReferencias")
                        .HasForeignKey("RequisicaoParteRequerenteId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_REQUERENTE_REFERENCIA_R01");

                    b.Navigation("PessoaReferencia");

                    b.Navigation("RequisicaoParteRequerente");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesOcorrencias.RequisicaoOcorrencia", b =>
                {
                    b.HasOne("TRF3.SISPREC.AcaoTipos.AcaoTipo", "AcaoTipo")
                        .WithMany("RequisicaoOcorrencia")
                        .HasForeignKey("AcaoTipoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_OCORRENCIA_R01");

                    b.HasOne("TRF3.SISPREC.OcorrenciaMotivos.OcorrenciaMotivo", "OcorrenciaMotivo")
                        .WithMany("RequisicaoOcorrencia")
                        .HasForeignKey("CodigoMotivoOcorrenciaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_OCORRENCIA_R02");

                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithMany("RequisicaoOcorrencia")
                        .HasForeignKey("NumeroProtocoloRequisicaoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_OCORRENCIA_R03");

                    b.Navigation("AcaoTipo");

                    b.Navigation("OcorrenciaMotivo");

                    b.Navigation("RequisicaoProtocolo");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartes.RequisicaoParte", b =>
                {
                    b.HasOne("TRF3.SISPREC.AdvogadosJudiciais.AdvogadoJudicial", "AdvogadoJudicial")
                        .WithOne("RequisicoesParte")
                        .HasForeignKey("TRF3.SISPREC.RequisicoesPartes.RequisicaoParte", "AdvogadoJudicialId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("REQ_REQUISICAO_PARTE_R03");

                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithMany("RequisicaoPartes")
                        .HasForeignKey("NumeroProtocoloRequisicao")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PARTE_R06");

                    b.HasOne("TRF3.SISPREC.Pessoas.Pessoa", "Pessoa")
                        .WithMany("RequisicoesParte")
                        .HasForeignKey("PessoaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PARTE_R02");

                    b.Navigation("AdvogadoJudicial");

                    b.Navigation("Pessoa");

                    b.Navigation("RequisicaoProtocolo");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesContratuais.RequisicaoParteContratual", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPartes.RequisicaoParte", "RequisicaoParte")
                        .WithMany("Contratual")
                        .HasForeignKey("RequisicaoParteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PARTE_CONTRATUAL_R02");

                    b.HasOne("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", "Requerente")
                        .WithMany("Contratual")
                        .HasForeignKey("RequisicaoRequerenteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PARTE_CONTRATUAL_R01");

                    b.Navigation("Requerente");

                    b.Navigation("RequisicaoParte");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPartes.RequisicaoParte", "RequisicaoParte")
                        .WithOne("Requerente")
                        .HasForeignKey("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", "RequisicaoParteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PARTE_REQUERENTE_R01");

                    b.Navigation("RequisicaoParte");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesRequerentesIR.RequisicaoParteRequerenteIr", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", "RequisicaoParteRequerente")
                        .WithOne("RequisicaoParteRequerenteIrs")
                        .HasForeignKey("TRF3.SISPREC.RequisicoesPartesRequerentesIR.RequisicaoParteRequerenteIr", "RequisicaoParteRequerenteId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_REQUERENTE_IR_R01");

                    b.Navigation("RequisicaoParteRequerente");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesRequerentesPSS.RequisicaoParteRequerentePss", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", "RequisicaoParteRequerente")
                        .WithOne("RequisicaoParteRequerentePss")
                        .HasForeignKey("TRF3.SISPREC.RequisicoesPartesRequerentesPSS.RequisicaoParteRequerentePss", "RequisicaoParteRequerenteId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_REQUERENTE_PSS_R01");

                    b.HasOne("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", "UnidadeOrcamentaria")
                        .WithMany()
                        .HasForeignKey("UnidadeOrcamentariaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_REQUERENTE_PSS_R02");

                    b.Navigation("RequisicaoParteRequerente");

                    b.Navigation("UnidadeOrcamentaria");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesReus.RequisicaoParteReu", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPartes.RequisicaoParte", "RequisicaoParte")
                        .WithOne("Reu")
                        .HasForeignKey("TRF3.SISPREC.RequisicoesPartesReus.RequisicaoParteReu", "RequisicaoParteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PARTE_REU_R01");

                    b.Navigation("RequisicaoParte");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPlanosOrcamentos.RequisicaoPlanoOrcamento", b =>
                {
                    b.HasOne("TRF3.SISPREC.BeneficiarioIdentificacaoTipos.BeneficiarioIdentificacaoTipo", "BeneficiarioIdentificacaoTipo")
                        .WithMany()
                        .HasForeignKey("BeneficiarioIdentificacaoTipoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PLANO_ORCAMENTO_R02");

                    b.HasOne("TRF3.SISPREC.DespesaNaturezas.DespesaNatureza", "DespesaNatureza")
                        .WithMany()
                        .HasForeignKey("DespesaNaturezaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PLANO_ORCAMENTO_R05");

                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithOne("RequisicaoPlanoOrcamento")
                        .HasForeignKey("TRF3.SISPREC.RequisicoesPlanosOrcamentos.RequisicaoPlanoOrcamento", "NumeroProtocoloRequisicaoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PLANO_ORCAMENTO_R06");

                    b.HasOne("TRF3.SISPREC.SentencaTipos.SentencaTipo", "SentencaTipo")
                        .WithMany()
                        .HasForeignKey("SentencaTipoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PLANO_ORCAMENTO_R01");

                    b.HasOne("TRF3.SISPREC.DespesaTipos.DespesaTipo", "TipoDespesa")
                        .WithMany()
                        .HasForeignKey("TipoDespesaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PLANO_ORCAMENTO_R04");

                    b.HasOne("TRF3.SISPREC.MovimentoTipos.MovimentoTipo", "TipoMovimento")
                        .WithMany()
                        .HasForeignKey("TipoMovimentoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PLANO_ORCAMENTO_R03");

                    b.Navigation("BeneficiarioIdentificacaoTipo");

                    b.Navigation("DespesaNatureza");

                    b.Navigation("RequisicaoProtocolo");

                    b.Navigation("SentencaTipo");

                    b.Navigation("TipoDespesa");

                    b.Navigation("TipoMovimento");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesProcessosOrigens.RequisicaoProcessoOrigem", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithMany("RequisicaoProcessosOrigem")
                        .HasForeignKey("NumeroProtocoloRequisicao")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PROCESSO_ORIGEM_R02");

                    b.HasOne("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicial", "UnidadeJudicial")
                        .WithMany("RequisicaoProtocoloOrigem")
                        .HasForeignKey("UnidadeJudicialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PROCESSO_ORIGEM_R01");

                    b.Navigation("RequisicaoProtocolo");

                    b.Navigation("UnidadeJudicial");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPropostaParcela.RequisicaoPropostaParcela", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPropostas.RequisicaoProposta", "RequisicaoProposta")
                        .WithMany("RequisicaoPropostaParcela")
                        .HasForeignKey("NumeroProtocoloRequisicaoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PROPOSTA_PARCELA_R01");

                    b.HasOne("TRF3.SISPREC.IndicadorEconomicoTipos.IndicadorEconomicoTipo", "IndicadorEconomicoTipo")
                        .WithMany("RequisicaoPropostaParcela")
                        .HasForeignKey("TipoIndicadorEconomicoId")
                        .HasConstraintName("REQ_REQUISICAO_PROPOSTA_PARCELA_R02");

                    b.Navigation("IndicadorEconomicoTipo");

                    b.Navigation("RequisicaoProposta");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPropostas.RequisicaoProposta", b =>
                {
                    b.HasOne("TRF3.SISPREC.IndicadorEconomicoTipos.IndicadorEconomicoTipo", "IndicadorEconomicoTipo")
                        .WithMany("RequisicaoProposta")
                        .HasForeignKey("IndicadorEconomicoTipoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("REQ_REQUISICAO_PROPOSTA_R03");

                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithOne("RequisicaoProposta")
                        .HasForeignKey("TRF3.SISPREC.RequisicoesPropostas.RequisicaoProposta", "NumeroProtocoloRequisicao")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PROPOSTA_R02");

                    b.HasOne("TRF3.SISPREC.Propostas.Proposta", "Proposta")
                        .WithMany()
                        .HasForeignKey("PropostaId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PROPOSTA_R01");

                    b.Navigation("IndicadorEconomicoTipo");

                    b.Navigation("Proposta");

                    b.Navigation("RequisicaoProtocolo");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesProtocolos.AssuntosExecucoes.AssuntoExecucao", b =>
                {
                    b.HasOne("TRF3.SISPREC.Assuntos.Assunto", "Assunto")
                        .WithMany()
                        .HasForeignKey("AssuntoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_ASSUNTO_EXECUCAO_R02");

                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithMany("AssuntoExecucoes")
                        .HasForeignKey("NumeroProtocoloRequisicao")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_ASSUNTO_EXECUCAO_R01");

                    b.Navigation("Assunto");

                    b.Navigation("RequisicaoProtocolo");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", b =>
                {
                    b.HasOne("TRF3.SISPREC.Assuntos.Assunto", "Assunto")
                        .WithMany()
                        .HasForeignKey("AssuntoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PROTOCOLO_R02");

                    b.HasOne("TRF3.SISPREC.SituacoesRequisicoesProtocolos.SituacaoRequisicaoProtocolo", "SituacaoRequisicaoProtocolo")
                        .WithMany()
                        .HasForeignKey("SituacaoRequisicaoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PROTOCOLO_R03");

                    b.HasOne("TRF3.SISPREC.TiposProcedimentos.TipoProcedimento", "TipoProcedimento")
                        .WithMany()
                        .HasForeignKey("TipoProcedimentoId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("REQ_REQUISICAO_PROTOCOLO_R01");

                    b.HasOne("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicial", "UnidadeJudicial")
                        .WithMany("RequisicaoProtocolo")
                        .HasForeignKey("UnidadeJudicialId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName("REQ_REQUISICAO_PROTOCOLO_R04");

                    b.Navigation("Assunto");

                    b.Navigation("SituacaoRequisicaoProtocolo");

                    b.Navigation("TipoProcedimento");

                    b.Navigation("UnidadeJudicial");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesVerificacoes.RequisicaoVerificacao", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", "RequisicaoProtocolo")
                        .WithMany("RequisicaoVerificacao")
                        .HasForeignKey("NumeroProtocoloRequisicaoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_VERIFICACAO_R01");

                    b.HasOne("TRF3.SISPREC.VerificacaoTipos.VerificacaoTipo", "VerificacaoTipo")
                        .WithMany("RequisicaoVerificacao")
                        .HasForeignKey("VerificacaoTipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("ANA_REQUISICAO_VERIFICACAO_R02");

                    b.Navigation("RequisicaoProtocolo");

                    b.Navigation("VerificacaoTipo");
                });

            modelBuilder.Entity("TRF3.SISPREC.SincronizacaoProgressos.SincronizacaoProgresso", b =>
                {
                    b.HasOne("TRF3.SISPREC.SincronizacoesDominios.SincronizacaoDominio", "SincronizacaoDominio")
                        .WithMany("SincronizacoesProgressos")
                        .HasForeignKey("IdSincronizacaoDominio")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("APP_SINCRONIZACAO_PROGRESSO_R01");

                    b.Navigation("SincronizacaoDominio");
                });

            modelBuilder.Entity("TRF3.SISPREC.TiposProcedimentos.TipoProcedimento", b =>
                {
                    b.HasOne("TRF3.SISPREC.BeneficiarioIdentificacaoTipos.BeneficiarioIdentificacaoTipo", "BeneficiarioIdentificacaoTipo")
                        .WithMany()
                        .HasForeignKey("BeneficiarioIdentificacaoTipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_TIPO_PROCEDIMENTO_R02");

                    b.HasOne("TRF3.SISPREC.MovimentoTipos.MovimentoTipo", "MovimentoTipo")
                        .WithMany()
                        .HasForeignKey("MovimeTipoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("REQ_TIPO_PROCEDIMENTO_R01");

                    b.Navigation("BeneficiarioIdentificacaoTipo");

                    b.Navigation("MovimentoTipo");
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesGestoras.UnidadeGestora", b =>
                {
                    b.HasOne("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", "Unidade")
                        .WithMany("unidadeGestoras")
                        .HasForeignKey("SEQ_UNIDAD")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_UNIDADE_GESTORA_R01");

                    b.Navigation("Unidade");
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesJudiciais.ContasUnidadesJudiciais.ContaUnidadeJudicial", b =>
                {
                    b.HasOne("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicial", "UnidadeJudicial")
                        .WithMany("ContasUnidadeJudicial")
                        .HasForeignKey("UnidadeJudicialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_UNIDADE_JUDICIAL_CONTA_R01");

                    b.HasOne("TRF3.SISPREC.Agencias.Agencia", "Agencia")
                        .WithMany("ContasUnidadeJudicial")
                        .HasForeignKey("NumBanco", "NumAgencia")
                        .HasConstraintName("TRF_UNIDADE_JUDICIAL_CONTA_R02");

                    b.Navigation("Agencia");

                    b.Navigation("UnidadeJudicial");
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicialOrigem", b =>
                {
                    b.HasOne("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicial", "UnidadeJudicial")
                        .WithOne("UnidadeJudicialOrigem")
                        .HasForeignKey("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicialOrigem", "UnidadeJudicialOrigemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("TRF_UNIDADE_JUDICIAL_ORIGEM_R01");

                    b.Navigation("UnidadeJudicial");
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", b =>
                {
                    b.HasOne("TRF3.SISPREC.Unidades.Unidade", "Unidade")
                        .WithOne("UnidadeOrcamentaria")
                        .HasForeignKey("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", "UnidadeOrcamentariaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("CJF_UNIDADE_R01");

                    b.HasOne("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", "UnidadeSuperior")
                        .WithMany()
                        .HasForeignKey("UnidadeSuperiorIdCJF")
                        .HasConstraintName("CJF_UNIDADE_R02");

                    b.Navigation("Unidade");

                    b.Navigation("UnidadeSuperior");
                });

            modelBuilder.Entity("TRF3.SISPREC.VerificacaoTipos.VerificacaoTipo", b =>
                {
                    b.HasOne("TRF3.SISPREC.AnaliseTelas.AnaliseTela", "AnaliseTela")
                        .WithMany("VerificacoesTipos")
                        .HasForeignKey("AnaliseTelaId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("ANA_VERIFICACAO_TIPO_R01");

                    b.Navigation("AnaliseTela");
                });

            modelBuilder.Entity("TRF3.SISPREC.VerificacoesCnpjCpf.VerificacaoCnpjCpf", b =>
                {
                    b.HasOne("TRF3.SISPREC.RequisicoesPartes.RequisicaoParte", "RequisicaoParte")
                        .WithMany("VerificacaoCnpjCpf")
                        .HasForeignKey("RequisicaoParteId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("ANA_VERIFICACAO_CNPJ_CPF_R02");

                    b.HasOne("TRF3.SISPREC.RequisicoesVerificacoes.RequisicaoVerificacao", "RequisicaoVerificacao")
                        .WithMany("VerificacaoCnpjCpf")
                        .HasForeignKey("RequisicaoVerificacaoId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("ANA_VERIFICACAO_CNPJ_CPF_R01");

                    b.Navigation("RequisicaoParte");

                    b.Navigation("RequisicaoVerificacao");
                });

            modelBuilder.Entity("TRF3.SISPREC.ViewAuditoriaPropriedades.ViewAuditoriaPropriedade", b =>
                {
                    b.HasOne("TRF3.SISPREC.ViewAuditoriaEntidades.ViewAuditoriaEntidade", "AuditoriaEntidade")
                        .WithMany("AuditoriaPropriedades")
                        .HasForeignKey("AuditoriaEntidadeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AuditoriaEntidade");
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.AuditLogAction", b =>
                {
                    b.HasOne("Volo.Abp.AuditLogging.AuditLog", null)
                        .WithMany("Actions")
                        .HasForeignKey("AuditLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityChange", b =>
                {
                    b.HasOne("Volo.Abp.AuditLogging.AuditLog", null)
                        .WithMany("EntityChanges")
                        .HasForeignKey("AuditLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityPropertyChange", b =>
                {
                    b.HasOne("Volo.Abp.AuditLogging.EntityChange", null)
                        .WithMany("PropertyChanges")
                        .HasForeignKey("EntityChangeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.TenantManagement.TenantConnectionString", b =>
                {
                    b.HasOne("Volo.Abp.TenantManagement.Tenant", null)
                        .WithMany("ConnectionStrings")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("TRF3.SISPREC.AcaoTipos.AcaoTipo", b =>
                {
                    b.Navigation("AcaoJustificativa");

                    b.Navigation("OcorrenciaMotivo");

                    b.Navigation("RequisicaoOcorrencia");
                });

            modelBuilder.Entity("TRF3.SISPREC.AcoesJustificativa.AcaoJustificativa", b =>
                {
                    b.Navigation("RequisicaoJustificativa");
                });

            modelBuilder.Entity("TRF3.SISPREC.AcoesOriginarias.AcaoOriginaria", b =>
                {
                    b.Navigation("Processo")
                        .IsRequired();

                    b.Navigation("UnidadesJudiciais");
                });

            modelBuilder.Entity("TRF3.SISPREC.AdvogadosJudiciais.AdvogadoJudicial", b =>
                {
                    b.Navigation("RequisicoesParte");
                });

            modelBuilder.Entity("TRF3.SISPREC.Agencias.Agencia", b =>
                {
                    b.Navigation("ContasUnidadeJudicial");
                });

            modelBuilder.Entity("TRF3.SISPREC.AnaliseTelas.AnaliseTela", b =>
                {
                    b.Navigation("OcorrenciaMotivo");

                    b.Navigation("RequisicaoJustificativa");

                    b.Navigation("VerificacoesTipos");
                });

            modelBuilder.Entity("TRF3.SISPREC.Assuntos.Assunto", b =>
                {
                    b.Navigation("AssuntoAuxiliar")
                        .IsRequired();

                    b.Navigation("AssuntoDespesas");

                    b.Navigation("Processos");
                });

            modelBuilder.Entity("TRF3.SISPREC.Bancos.Banco", b =>
                {
                    b.Navigation("Agencias");
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiarioIdentificacaoTipos.BeneficiarioIdentificacaoTipo", b =>
                {
                    b.Navigation("Beneficiarios");
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiarioSucessaoTipos.BeneficiarioSucessaoTipo", b =>
                {
                    b.Navigation("Beneficiarios");
                });

            modelBuilder.Entity("TRF3.SISPREC.BeneficiarioTipos.BeneficiarioTipo", b =>
                {
                    b.Navigation("Beneficiarios");
                });

            modelBuilder.Entity("TRF3.SISPREC.Beneficiarios.Beneficiario", b =>
                {
                    b.Navigation("Antecessores");

                    b.Navigation("Parcelas");

                    b.Navigation("Partes");
                });

            modelBuilder.Entity("TRF3.SISPREC.BlocosSisprec.BlocoSisprec", b =>
                {
                    b.Navigation("ExpedienteAdministrativo");
                });

            modelBuilder.Entity("TRF3.SISPREC.CodigosReceitaFederal.CodigoReceitaFederal", b =>
                {
                    b.Navigation("RequisicaoParteRequerenteCompensado");
                });

            modelBuilder.Entity("TRF3.SISPREC.ContaBancariaBeneficiarios.ContaBancariaBeneficiario", b =>
                {
                    b.Navigation("Beneficiario")
                        .IsRequired();
                });

            modelBuilder.Entity("TRF3.SISPREC.ContaBancarias.ContaBancaria", b =>
                {
                    b.Navigation("Processo")
                        .IsRequired();
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleImportacaoRequisicoes.ControleImportacaoRequisicao", b =>
                {
                    b.Navigation("ControleImportacaoRequisicoesErro");
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleProcessamentoArquivos.ControleProcessamentoArquivo", b =>
                {
                    b.Navigation("Processamentos");
                });

            modelBuilder.Entity("TRF3.SISPREC.ControleProcessamentos.ControleProcessamento", b =>
                {
                    b.Navigation("ControleProcessos");

                    b.Navigation("PagosBeneficiarioOrigemPCT");

                    b.Navigation("PagosBeneficiarioOrigemRPV");

                    b.Navigation("PrecatoriosOrigemPCT");

                    b.Navigation("PrecatoriosOrigemRPV");

                    b.Navigation("Processos");
                });

            modelBuilder.Entity("TRF3.SISPREC.DespesaClassificacoes.DespesaClassificacao", b =>
                {
                    b.Navigation("Processos");
                });

            modelBuilder.Entity("TRF3.SISPREC.DespesaNaturezas.DespesaNatureza", b =>
                {
                    b.Navigation("DespesaClassificacoes");
                });

            modelBuilder.Entity("TRF3.SISPREC.DespesaTipos.DespesaTipo", b =>
                {
                    b.Navigation("DespesaClassificacoes");
                });

            modelBuilder.Entity("TRF3.SISPREC.ExpedientesAdministrativos.ExpedienteAdministrativo", b =>
                {
                    b.Navigation("ExpedienteAdministrativoHistoricos");

                    b.Navigation("RequisicaoExpedienteAdministrativo");
                });

            modelBuilder.Entity("TRF3.SISPREC.Fases.Fase", b =>
                {
                    b.Navigation("Processamentos");

                    b.Navigation("Processos");
                });

            modelBuilder.Entity("TRF3.SISPREC.IndicadorEconomicoTipos.IndicadorEconomicoTipo", b =>
                {
                    b.Navigation("IndicadorEconomico");

                    b.Navigation("RequisicaoProposta");

                    b.Navigation("RequisicaoPropostaParcela");
                });

            modelBuilder.Entity("TRF3.SISPREC.IndicadorEconomicos.IndicadorEconomico", b =>
                {
                    b.Navigation("Proposta");
                });

            modelBuilder.Entity("TRF3.SISPREC.IndiceAtualizacaoMonetariaTipos.IndiceAtualizacaoMonetariaTipo", b =>
                {
                    b.Navigation("IndicesAtualizacaoMonetaria");
                });

            modelBuilder.Entity("TRF3.SISPREC.JustificativaDocumentos.JustificativaDocumento", b =>
                {
                    b.Navigation("JustificativaComparacao");
                });

            modelBuilder.Entity("TRF3.SISPREC.LogGerais.LogGeral", b =>
                {
                    b.Navigation("LogDetalhe");
                });

            modelBuilder.Entity("TRF3.SISPREC.MovimentoTipos.MovimentoTipo", b =>
                {
                    b.Navigation("Beneficiarios");

                    b.Navigation("Processos");
                });

            modelBuilder.Entity("TRF3.SISPREC.Municipios.Municipio", b =>
                {
                    b.Navigation("Agencias");

                    b.Navigation("CidadeRegPag");

                    b.Navigation("EnderecoPessoa");
                });

            modelBuilder.Entity("TRF3.SISPREC.OcorrenciaMotivos.OcorrenciaMotivo", b =>
                {
                    b.Navigation("RequisicaoOcorrencia");
                });

            modelBuilder.Entity("TRF3.SISPREC.OrdemPagamento107aTipos.OrdemPagamento107aTipo", b =>
                {
                    b.Navigation("Parcelas");
                });

            modelBuilder.Entity("TRF3.SISPREC.Pessoas.Pessoa", b =>
                {
                    b.Navigation("Endereco");

                    b.Navigation("RequisicaoRequerenteReferencias");

                    b.Navigation("RequisicoesParte");

                    b.Navigation("SinPessoasReqPag");
                });

            modelBuilder.Entity("TRF3.SISPREC.Planos.Plano", b =>
                {
                    b.Navigation("Fases");
                });

            modelBuilder.Entity("TRF3.SISPREC.PrecatoriosOrigensPCT.PrecatorioOrigemPCT", b =>
                {
                    b.Navigation("BeneficiariosPCT");
                });

            modelBuilder.Entity("TRF3.SISPREC.PrecatoriosOrigensRPV.PrecatorioOrigemRPV", b =>
                {
                    b.Navigation("BeneficiariosRPV");
                });

            modelBuilder.Entity("TRF3.SISPREC.Processos.Processo", b =>
                {
                    b.Navigation("Beneficiarios");

                    b.Navigation("Parcelas");

                    b.Navigation("Partes");

                    b.Navigation("ProcessosAnteriores");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicaoJustificativas.RequisicaoJustificativa", b =>
                {
                    b.Navigation("Documentos");

                    b.Navigation("JustificativaComparacao");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartes.RequisicaoParte", b =>
                {
                    b.Navigation("Contratual");

                    b.Navigation("Requerente");

                    b.Navigation("Reu");

                    b.Navigation("VerificacaoCnpjCpf");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesContratuais.RequisicaoParteContratual", b =>
                {
                    b.Navigation("RequisicaoContratualParcela");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPartesRequerentes.RequisicaoParteRequerente", b =>
                {
                    b.Navigation("Contratual");

                    b.Navigation("RequisicaoParteRequerenteIrs");

                    b.Navigation("RequisicaoParteRequerentePss");

                    b.Navigation("RequisicaoRequerenteCompensado");

                    b.Navigation("RequisicaoRequerenteParcela");

                    b.Navigation("RequisicaoRequerenteReferencias");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesPropostas.RequisicaoProposta", b =>
                {
                    b.Navigation("RequisicaoPropostaParcela");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesProtocolos.RequisicaoProtocolo", b =>
                {
                    b.Navigation("AssuntoExecucoes");

                    b.Navigation("RequisicaoEstorno");

                    b.Navigation("RequisicaoExpedienteAdministrativo");

                    b.Navigation("RequisicaoJustificativas");

                    b.Navigation("RequisicaoObservacao")
                        .IsRequired();

                    b.Navigation("RequisicaoOcorrencia");

                    b.Navigation("RequisicaoPartes");

                    b.Navigation("RequisicaoPlanoOrcamento");

                    b.Navigation("RequisicaoProcessosOrigem");

                    b.Navigation("RequisicaoProposta");

                    b.Navigation("RequisicaoVerificacao");
                });

            modelBuilder.Entity("TRF3.SISPREC.RequisicoesVerificacoes.RequisicaoVerificacao", b =>
                {
                    b.Navigation("VerificacaoCnpjCpf");
                });

            modelBuilder.Entity("TRF3.SISPREC.SentencaTipos.SentencaTipo", b =>
                {
                    b.Navigation("Processos");
                });

            modelBuilder.Entity("TRF3.SISPREC.ServidorCondicaoTipos.ServidorCondicaoTipo", b =>
                {
                    b.Navigation("Beneficiarios");
                });

            modelBuilder.Entity("TRF3.SISPREC.Setores.Setor", b =>
                {
                    b.Navigation("ModelosDocumentos");
                });

            modelBuilder.Entity("TRF3.SISPREC.SincronizacoesDominios.SincronizacaoDominio", b =>
                {
                    b.Navigation("SincronizacoesProgressos");
                });

            modelBuilder.Entity("TRF3.SISPREC.TiposEtapaProcessamentos.TipoEtapaProcessamento", b =>
                {
                    b.Navigation("Processamentos");
                });

            modelBuilder.Entity("TRF3.SISPREC.UFs.UF", b =>
                {
                    b.Navigation("Municipio");
                });

            modelBuilder.Entity("TRF3.SISPREC.Unidades.Unidade", b =>
                {
                    b.Navigation("Pessoa");

                    b.Navigation("Propostas");

                    b.Navigation("UnidadeOrcamentaria");
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesGestoras.UnidadeGestora", b =>
                {
                    b.Navigation("Fases");

                    b.Navigation("Planos");
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesJudiciais.UnidadeJudicial", b =>
                {
                    b.Navigation("ContasUnidadeJudicial");

                    b.Navigation("RequisicaoProtocolo");

                    b.Navigation("RequisicaoProtocoloOrigem");

                    b.Navigation("UnidadeJudicialOrigem")
                        .IsRequired();
                });

            modelBuilder.Entity("TRF3.SISPREC.UnidadesOrcamentarias.UnidadeOrcamentaria", b =>
                {
                    b.Navigation("Beneficiarios");

                    b.Navigation("ProcessosCadastrados");

                    b.Navigation("ProcessosExecutados");

                    b.Navigation("unidadeGestoras");
                });

            modelBuilder.Entity("TRF3.SISPREC.ValorTipos.ValorTipo", b =>
                {
                    b.Navigation("Parcelas");
                });

            modelBuilder.Entity("TRF3.SISPREC.VerificacaoTipos.VerificacaoTipo", b =>
                {
                    b.Navigation("RequisicaoVerificacao");
                });

            modelBuilder.Entity("TRF3.SISPREC.ViewAuditoriaEntidades.ViewAuditoriaEntidade", b =>
                {
                    b.Navigation("AuditoriaPropriedades");
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.AuditLog", b =>
                {
                    b.Navigation("Actions");

                    b.Navigation("EntityChanges");
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityChange", b =>
                {
                    b.Navigation("PropertyChanges");
                });

            modelBuilder.Entity("Volo.Abp.TenantManagement.Tenant", b =>
                {
                    b.Navigation("ConnectionStrings");
                });
#pragma warning restore 612, 618
        }
    }
}
