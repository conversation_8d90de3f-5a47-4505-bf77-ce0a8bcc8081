@page
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model TRF3.SISPREC.Web.Pages.AnalisePendencias.IndexModel
@{
    PageLayout.Content.Title = "Análise de Pendências";

    ViewData["Meses"] = Model.MesesComItemVazio;
}

@section scripts
{
    <abp-script src="/js/util.js" />
    <abp-script src="/js/componente-utils.js" />
    <abp-script src="/js/analises-utils.js" />
    <abp-script src="/Pages/AnalisePendencias/index.js" />
}
 
@section styles
{
    <abp-style src="/css/app/analises.css" />
    <abp-style src="/Pages/AnalisePendencias/index.css" />
}

<abp-card>
    <abp-card-body>

        <!--BOTÃO RECOLHER/EXPANDIR-->
        <div class="tooltip-container float-end" style="margin:-1.5rem">
            <abp-button class="ocultar-secao-botao" id="btnOcultarSecaoTopo" size="Small" button-type="Default" abp-collapse-id="ocultarSecaoTopo">ᐱ</abp-button>
            <span class="tooltiptext" id="tooltipText">Recolher</span>
        </div>

        <!--SEÇÃO OCULTAR E EXIBIR-->
        <abp-collapse-body id="ocultarSecaoTopo" show="true">
            <form asp-for="AnalisePendenciasFilterInput" id="AnalisePendenciasFilterInput">
                <partial name="Shared/Partials/_AnaliseFiltroForm" model="@Model.AnalisePendenciasFilterInput" />
            </form>

            <!--LISTAS-->
            <abp-row class="lista-dados">
                <abp-column style="max-width: 20%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="PendenciaRequisicoesTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
                <abp-column style="max-width: 20%;">
                    <abp-row>
                        <abp-column>
                            <div id="containerProtocolosPorCpf">
                                <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="ProtocolosPorCpfTable" class="nowrap" />
                            </div>
                            <div id="containerProtocolosPorOriginario" style="display:none">
                                <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="ProtocolosPorOriginarioTable" class="nowrap" />
                            </div>
                            <div id="containerProtocolosPorEstornada" style="display:none">
                                <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="ProtocolosPorEstornadaTable" class="nowrap" />
                            </div>
                        </abp-column>
                    </abp-row>
                </abp-column>
                <abp-column style="max-width: 60%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="OcorrenciasTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
            </abp-row>
        </abp-collapse-body>

        <!--BOTÕES-->
        <abp-row class="d-flex button-container barra-navegacao">

            <!--BOTÃO REQUISIÇÃO-->
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="PendenciaRequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="width: 135px; margin: -6px;">
                <abp-input asp-for="ViewModel!.NumeroDaRequisicao" readonly="true" />
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="PendenciaRequisicoesTable"></abp-button>
            </abp-column>

            <!--BOTÃO CPF-->
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="ProtocolosPorCpfTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="margin: -6px;">
                <abp-button button-type="Primary" size="Small" disabled="true" text="CPF" class="botao-lista-comparacao selecionado" id="ListaPorCPF"></abp-button>
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="ProtocolosPorCpfTable"></abp-button>
            </abp-column>

            <!--BOTÃO ORIGINÁRIO-->
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="ProtocolosPorOriginarioTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="margin: -6px;">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Originário" class="botao-lista-comparacao" id="ListaPorOriginario"></abp-button>
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="ProtocolosPorOriginarioTable"></abp-button>
            </abp-column>

            <!--BOTÃO ESTORNADAS-->
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="ProtocolosPorEstornadaTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="margin: -6px;">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Estornadas" class="botao-lista-comparacao" id="ListaPorEstornada"></abp-button>
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="ProtocolosPorEstornadaTable"></abp-button>
            </abp-column>

            <abp-column class="col-auto" style="width: 135px; margin-right: -6px;">
                <abp-input asp-for="ViewModel!.NumeroRequisicaoComparada" readonly="true" />
            </abp-column>

            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Salvar Requisição Comparada" id="btnSalvarComparada"></abp-button>
            </abp-column>

            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Cadastrar Justificativa" id="btnCadastrarJustificativa" data-tipo-analise="2"></abp-button>
            </abp-column>
        </abp-row>

        @await Html.PartialAsync("Components/Analises/_SecaoComparacaoView")

    </abp-card-body>
</abp-card>
