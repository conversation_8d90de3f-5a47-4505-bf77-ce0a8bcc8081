$(function () {
    const service = tRF3.sISPREC.confirmarLiberacoes.confirmarLiberacao;

    let requisicoesDataTable = inicializarRequisicoesDataTable();

    const ocorrenciasDataTable = inicializarOcorrenciasDataTable();

    const analisesDataTable = inicializarAnalisesDataTable();

    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'Ano', 'Mes');

    $('#btnPesquisar').on('click', function (event) {
        event.preventDefault();
        novaPesquisa();
    });

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function (event, jqXHR) {
        // Garante que a UI será desbloqueada
        abp.ui.unblock();

        resetRequisicoesDataTable();
    });

    function inicializarRequisicoesDataTable() {
        return $('#LiberacaoRequisicoesTable')
            // Bloqueia a tela antes de enviar a requisição ajax.
            .on('preXhr.dt', function () { abp.ui.block({ elm: 'body', busy: false }); })
            // Intercepta o evento após a requisição (sucesso ou erro) --> só está disparando após sucesso. Talvez o abp.libs.datatables.createAjax() esteja "tratando" o erro?
            .on('xhr.dt', function () { abp.ui.unblock(); })
            // Desbloqueia a tela em caso de erro (ex: timeout).
            .on('error.dt', function () { abp.ui.unblock(); })
            // Configura o datatable.
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                processing: true,
                serverSide: true,
                deferLoading: 0, // Delay the loading of server-side data until second draw.
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'numeroProtocoloRequisicaoPendente',
                ajax: abp.libs.datatables.createAjax(service.getRequisicoes, obterFiltrosPesquisa),
                columnDefs: [
                    {
                        title: "Requisição",
                        data: "numeroProtocoloRequisicaoPendente"
                    }
                ],
                drawCallback: function (settings) {
                    let tabela = this.api();
                    // Seleciona a primeira linha.
                    selecionarLinhaTabela(tabela, 0);
                }
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarRequisicaoEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="LiberacaoRequisicoesTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="LiberacaoRequisicoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function inicializarOcorrenciasDataTable() {
        return $('#OcorrenciaTable').DataTable(abp.libs.datatables.normalizeConfiguration({
            paging: false,
            searching: false, // Disable default searchbox.
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            columnDefs: [
                {
                    title: "Tipo Ocorrência",
                    data: "tipoOcorrencia"
                },
                {
                    title: "Detalhe",
                    data: "descricao"
                },
                {
                    title: "Data Ocorrência",
                    data: "dataOCorrencia",
                    render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')
                }
            ]
        }));
    }

    function inicializarAnalisesDataTable() {
        return $('#DadosAnalise').DataTable(abp.libs.datatables.normalizeConfiguration({
            paging: false,
            searching: false, // Disable default searchbox.
            autoWidth: false,
            scrollY: 100,
            ordering: false,
            columnDefs: [
                {
                    title: "Tipo Análise",
                    data: "descricaoTela"
                },
                {
                    title: "Data Análise",
                    data: "dataAnalise",
                    render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss')
                },
                {
                    title: "Usuário",
                    data: "usuarioAnalise"
                },
                {
                    title: "Decisão",
                    data: "tipoAcao"
                },
                {
                    title: "Motivo",
                    data: "descricaoJustificativa"
                },
                {
                    title: "Complemento do Motivo",
                    data: "complementoJustificativa"
                }
            ]
        }));
    }

    function obterFiltrosPesquisa() {
        const input = {};
        $('#ConfirmarLiberacaoFilterInput')
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/ConfirmarLiberacaoFilterInput./g, ''))] = data.value;
                }
            })
        return input;
    };

    function obterRequisicaoEmAnalise() { return requisicoesDataTable.row({ selected: true }).id() };

    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        // Recarrega a tabela de requisições.
        requisicoesDataTable.ajax.reload();
    }

    function atualizarRequisicaoEmAnalise() {
        limparRequisicaoEmAnalise();

        let requisicaoEmAnalise = obterRequisicaoEmAnalise();

        if (requisicaoEmAnalise) {
            $('#ViewModel_NumeroDaRequisicao').val(requisicaoEmAnalise);

            $('.barra-navegacao button').prop('disabled', false);

            obterDadosRequisicao(requisicaoEmAnalise);
        }
    }

    async function obterDadosRequisicao(requisicao) {
        try {
            ocorrenciasDataTable.clear().draw();
            analisesDataTable.clear().draw();

            // Carrega as listas de requisições e ocorrências.
            abp.ui.block({ elm: 'body', busy: true })

            const results = await Promise.allSettled([
                service.getOcorrencias(requisicao),
                service.getJustificastivaAnalises(requisicao),
                service.getInformacaoBasica(requisicao),
            ]);

            if (!preencherTabela(results[0], ocorrenciasDataTable))
                ocorrenciasDataTable.clear().draw();
            if (!preencherTabela(results[1], analisesDataTable))
                analisesDataTable.clear().draw();
            if (results[2].status === 'fulfilled' && results[2].value) {
                const dados = results[2].value;

                $('#ViewModel_TipoProcedimento').val(dados.tipoProcedimento);
                $('#ViewModel_Ano').val(dados.anoPropos);
                $('#ViewModel_Mes').val(dados.mesPropos);
                $('#ViewModel_SituacaoRequisicao').val(dados.situacaoRequisicao);
                $('#ViewModel_SituacaoProposta').val(dados.situacaoProposta);
            }
        }
        catch (ex) {
            abp.notify.error('Falha para obter dados da requisição.');
        }
        finally {
            abp.ui.unblock();
        }

        function preencherTabela(result, tabela) {
            if (result.status === 'fulfilled' && result.value.totalCount > 0) {
                tabela.clear().rows.add(result.value.items).draw();
                return true;
            }
            return false;
        }
    }

    function limparRequisicaoEmAnalise() {
        $('#ViewModel_NumeroDaRequisicao').val('');
        $('.barra-navegacao button').prop('disabled', true);

        $('#ViewModel_TipoProcedimento').val('');
        $('#ViewModel_Ano').val('');
        $('#ViewModel_Mes').val('');
        $('#ViewModel_SituacaoRequisicao').val('');
        $('#ViewModel_SituacaoProposta').val('');

        ocorrenciasDataTable.clear().draw();
        analisesDataTable.clear().draw();
    }

    function resetRequisicoesDataTable() {
        requisicoesDataTable.destroy();
        $('#LiberacaoRequisicoesTable').empty();

        limparRequisicaoEmAnalise();
        requisicoesDataTable = inicializarRequisicoesDataTable();
    }
});
