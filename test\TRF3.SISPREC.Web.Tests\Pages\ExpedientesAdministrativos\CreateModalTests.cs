using HtmlAgilityPack;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.Configuration;
using Shouldly;

namespace TRF3.SISPREC.Pages.Tests.ExpedientesAdministrativos;

public class CreateModalTests : SISPRECWebTestBase
{
    public CreateModalTests()
    {
        _factory = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureAppConfiguration((context, configBuilder) =>
            {
                var testConfig = new Dictionary<string, string>
                {
                    { "ConsultaSeiService:url", "https://mock-url" },
                    { "ConsultaSeiService:Servico", "MockServico" },
                    { "ConsultaSeiService:Chave", "MockChave" }
                };
                configBuilder.AddInMemoryCollection(testConfig);
            });
        });
    }

    private WebApplicationFactory<Program> _factory = new WebApplicationFactory<Program>();

    [Fact]
    public async Task Create_Modal_Teste()
    {
        // Arrange
        var client = _factory.CreateClient();
        var url = "/ExpedientesAdministrativos/CreateExpediente";

        // Act
        var response = await client.GetAsync(url);
        var responseAsString = await response.Content.ReadAsStringAsync();

        // Assert
        var htmlDocument = new HtmlDocument();
        htmlDocument.LoadHtml(responseAsString);

        response.ShouldNotBeNull();
        responseAsString.ShouldNotBeNull();
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK);
    }
}
