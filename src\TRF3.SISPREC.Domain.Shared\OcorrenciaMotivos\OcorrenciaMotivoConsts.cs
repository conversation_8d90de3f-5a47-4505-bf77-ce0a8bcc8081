namespace TRF3.SISPREC.OcorrenciaMotivos
{
    public static class OcorrenciaMotivoConsts
    {
        public const int CODIGO_MOTIVO_COMPLEMENTAR_IGUAL = 501;
        public const int CODIGO_MOTIVO_SUPLEMENTAR_IGUAL = 502;
        public const int CODIGO_MOTIVO_INCONTROVERSO_MENOR_IGUAL = 503;
        public const int CODIGO_MOTIVO_PERITO_ADVOGADO = 505;
        public const int CODIGO_MOTIVO_PERITO_CNPJ = 506;
        public const int CODIGO_MOTIVO_ADVOGADO_IGUAL_AUTOR = 508;
        public const int CODIGO_MOTIVO_AJUIZAMENTO = 509;
        public const int CODIGO_MOTIVO_ORGAO_PSS = 510;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_11 = 601;
        public const int CODIGO_MOTIVO_PREVENC<PERSON><PERSON>_TIPO_12 = 602;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_21 = 603;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_22 = 604;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_23 = 605;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_24 = 606;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_25 = 611;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_31 = 607;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_32 = 608;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_33 = 612;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_34 = 609;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_35 = 610;
        public const int CODIGO_MOTIVO_PREVENCAO_TIPO_36 = 613;
        public const int DESCRICAO_MOTIVO_TAMANHO_MAX = 300;
        public const int CODIGO_MOTIVO_TAMANHO_MAX = 1000000;

        public static int ObterCodigoPorPrevento(int? prevento)
        {
            return prevento switch
            {
                11 => CODIGO_MOTIVO_PREVENCAO_TIPO_11,
                12 => CODIGO_MOTIVO_PREVENCAO_TIPO_12,
                21 => CODIGO_MOTIVO_PREVENCAO_TIPO_21,
                22 => CODIGO_MOTIVO_PREVENCAO_TIPO_22,
                23 => CODIGO_MOTIVO_PREVENCAO_TIPO_23,
                24 => CODIGO_MOTIVO_PREVENCAO_TIPO_24,
                25 => CODIGO_MOTIVO_PREVENCAO_TIPO_25,
                31 => CODIGO_MOTIVO_PREVENCAO_TIPO_31,
                32 => CODIGO_MOTIVO_PREVENCAO_TIPO_32,
                33 => CODIGO_MOTIVO_PREVENCAO_TIPO_33,
                34 => CODIGO_MOTIVO_PREVENCAO_TIPO_34,
                35 => CODIGO_MOTIVO_PREVENCAO_TIPO_35,
                36 => CODIGO_MOTIVO_PREVENCAO_TIPO_36,
                _ => throw new ArgumentException("Prevento não mapeado para motivo de ocorrência.")
            };
        }


        public static string ObterTipoPrevencaoPorCodigoMotivo(int codigoMotivo)
        {
            return codigoMotivo switch
            {
                CODIGO_MOTIVO_PREVENCAO_TIPO_11 => "Tipo 11",
                CODIGO_MOTIVO_PREVENCAO_TIPO_12 => "Tipo 12",
                CODIGO_MOTIVO_PREVENCAO_TIPO_21 => "Tipo 21",
                CODIGO_MOTIVO_PREVENCAO_TIPO_22 => "Tipo 22",
                CODIGO_MOTIVO_PREVENCAO_TIPO_23 => "Tipo 23",
                CODIGO_MOTIVO_PREVENCAO_TIPO_24 => "Tipo 24",
                CODIGO_MOTIVO_PREVENCAO_TIPO_25 => "Tipo 25",
                CODIGO_MOTIVO_PREVENCAO_TIPO_31 => "Tipo 31",
                CODIGO_MOTIVO_PREVENCAO_TIPO_32 => "Tipo 32",
                CODIGO_MOTIVO_PREVENCAO_TIPO_33 => "Tipo 33",
                CODIGO_MOTIVO_PREVENCAO_TIPO_34 => "Tipo 34",
                CODIGO_MOTIVO_PREVENCAO_TIPO_35 => "Tipo 35",
                CODIGO_MOTIVO_PREVENCAO_TIPO_36 => "Tipo 36",
                _ => throw new ArgumentException($"Valor inválido para CodTipoPrevencao: {codigoMotivo}")
            };
        }
    }
}
