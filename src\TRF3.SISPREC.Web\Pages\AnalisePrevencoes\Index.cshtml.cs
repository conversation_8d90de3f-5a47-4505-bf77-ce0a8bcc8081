using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Web.Pages.AnalisePrevencoes.ViewModels;
using TRF3.SISPREC.Web.Pages.FilterInputs;

namespace TRF3.SISPREC.Web.Pages.AnalisePrevencoes
{
    [ExcludeFromCodeCoverage]
    public class IndexModel : SISPRECPageModel
    {
        public AnalisePrevencaoViewModel? ViewModel { get; set; } = new();
        public AnaliseFilterInput? AnalisePrevencaoFilterInput { get; set; }
        public async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
