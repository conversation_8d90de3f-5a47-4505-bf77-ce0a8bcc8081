using Microsoft.EntityFrameworkCore;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.AnaliseReinclusoes;
using TRF3.SISPREC.EntityFrameworkCore.Tests.TestHelpers;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.OcorrenciaMotivos;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicaoEstornos;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using Volo.Abp.EntityFrameworkCore;

namespace TRF3.SISPREC.EntityFrameworkCore.Repositories;

public class AnaliseReinclusaoRepositoryTests : SISPRECEntityFrameworkCoreTestBase
{
    private readonly IDbContextProvider<SISPRECDbContext> _dbContextProvider;
    private readonly AnaliseReinclusaoRepository _repository;
    private readonly SISPRECDbContext _dbContext;

    public AnaliseReinclusaoRepositoryTests()
    {
        // Configuração inicial dos mocks
        _dbContext = Substitute.For<SISPRECDbContext>(new DbContextOptions<SISPRECDbContext>());
        _dbContextProvider = Substitute.For<IDbContextProvider<SISPRECDbContext>>();
        _dbContextProvider.GetDbContextAsync().Returns(_dbContext);

        _repository = new AnaliseReinclusaoRepository(_dbContextProvider);
    }

    [Fact]
    public async Task Deve_Obter_Requisicoes_Com_Sucesso()
    {
        // Arrange
        var numeroProtocolo = "**********";
        ConfigurarMockPadrao(numeroProtocolo);

        // Act
        var resultado = await _repository.ObterAnaliseReinclusao(
            ETipoProcedimentoRequisicao.RPV,
            DateTime.Now.Year,
            DateTime.Now.Month,
            null,
            null,
            null);

        // Assert
        resultado.ShouldNotBeNull();
    }

    [Fact]
    public async Task Deve_Obter_Requisicoes_Filtro_Requisicao_Com_Sucesso()
    {
        // Arrange
        var numeroProtocolo = "**********";
        ConfigurarMockPadrao(numeroProtocolo);

        // Act
        var resultado = await _repository.ObterAnaliseReinclusao(
            ETipoProcedimentoRequisicao.RPV,
            DateTime.Now.Year,
            DateTime.Now.Month,
            null,
            null,
            "**********");

        // Assert
        resultado.ShouldNotBeNull();
    }

    [Fact]
    public async Task Deve_Obter_Requisicoes_Filtro_Data_Inicio_E_Data_Fim_Com_Sucesso()
    {
        // Arrange
        var numeroProtocolo = "**********";
        ConfigurarMockPadrao(numeroProtocolo);

        // Act
        var resultado = await _repository.ObterAnaliseReinclusao(
            ETipoProcedimentoRequisicao.RPV,
            DateTime.Now.Year,
            DateTime.Now.Month,
            DateTime.Now.AddHours(-1),
            DateTime.Now.AddHours(2),
            null);

        // Assert
        resultado.ShouldNotBeNull();
    }

    [Fact]
    public async Task Deve_Obter_Estorno_Requisicao_Com_Sucesso()
    {
        var numeroProcotolo = "**********";
        var numeroProcotoloOriginal = "**********";

        var requisicaoEstorno = new List<RequisicaoEstorno>
        {
            new()
            {
                NumeroProtocoloRequisicaoId = numeroProcotolo,
                NumeroRequisicaoOriginal = numeroProcotoloOriginal
            }
        };

        _dbContext.MockDbSet(requisicaoEstorno);

        var resultado = await _repository.ObterEstornoRequisicao(numeroProcotolo, numeroProcotoloOriginal);

        //Assert
        resultado.ShouldNotBeNull();
    }

    private void ConfigurarMockPadrao(string numeroProtocolo, List<RequisicaoProtocolo>? requisicaoProtocolos = null)
    {
        var dataBase = DateTime.Now;

        // Mock RequisicaoProtocolos
        requisicaoProtocolos ??=
            [
                new()
                {
                    NumeroProtocoloRequisicao = numeroProtocolo,
                    SituacaoRequisicaoId = (int)ESituacaoRequisicao.PENDENTE,
                    DataHoraProtocoloRequisicao = dataBase,
                    StatusProtocoloRequisicao = EStatusProtocoloRequisicao.PROTOCOLADA,
                    IndicadorInclusaoRequisicao = EIndicadorInclusaoRequisicao.ORIGINAL,
                    TipoProcedimentoId = ETipoProcedimentoRequisicao.RPV.ToString()
                },
                new()
                {
                    NumeroProtocoloRequisicao = "**********",
                    SituacaoRequisicaoId = (int)ESituacaoRequisicao.PENDENTE,
                    DataHoraProtocoloRequisicao = dataBase,
                    StatusProtocoloRequisicao = EStatusProtocoloRequisicao.PROTOCOLADA,
                    IndicadorInclusaoRequisicao = EIndicadorInclusaoRequisicao.REINCLUSAO,
                    TipoProcedimentoId = ETipoProcedimentoRequisicao.RPV.ToString()
                }
            ];

        // Mock Propostas
        var propostas = new List<Proposta>
            {
                new()
                {
                    PropostaId = 1,
                    TipoProcedimentoId = ETipoProcedimentoRequisicao.RPV.ToString(),
                    AnoProposta = dataBase.Year,
                    MesProposta = dataBase.Month,
                    SituacaoProposta = ESituacaoProposta.PENDENTE
                },
                new()
                {
                    PropostaId = 2,
                    TipoProcedimentoId = ETipoProcedimentoRequisicao.RPV.ToString(),
                    AnoProposta = dataBase.Year,
                    MesProposta = dataBase.Month,
                    SituacaoProposta = ESituacaoProposta.PENDENTE
                }
            };

        // Mock RequisicaoPropostas
        var requisicoesPropostas = new List<RequisicaoProposta>
            {
                new()
                {
                    NumeroProtocoloRequisicao = numeroProtocolo,
                    PropostaId = 1
                },
                new()
                {
                    NumeroProtocoloRequisicao = "**********",
                    PropostaId = 2
                }
            };

        // Mock MotivoOcorrencias com AnaliseTelaId null (caso base)
        var motivoOcorrencias = new List<OcorrenciaMotivo>
        {
            new()
            {
                OcorrenciaMotivoId = 1,
                AnaliseTelaId = null // Motivo genérico sem análise específica
            }
        };

        // Mock RequisicaoOcorrencias com motivo genérico
        var requisicoesOcorrencias = new List<RequisicaoOcorrencia>
        {
            new()
            {
                NumeroProtocoloRequisicaoId = numeroProtocolo,
                CodigoMotivoOcorrenciaId = 1,
                OcorrenciaMotivo = motivoOcorrencias[0],
            }
        };

        var requisicaoEstorno = new List<RequisicaoEstorno>
        {
            new()
            {
                NumeroProtocoloRequisicaoId = "**********",
                NumeroRequisicaoOriginal = numeroProtocolo
            }
        };

        var requisicaoJustificativa = new List<RequisicaoJustificativa>()
        {
            new()
            {
                NumeroProtocoloRequisicao = "**********",
                AnaliseTelaId = (int)EDescricaoAnaliseTela.CPF_CNPJ
            }
        };

        // Configura os mocks com dados base
        _dbContext.MockDbSet(requisicaoProtocolos);
        _dbContext.MockDbSet(propostas);
        _dbContext.MockDbSet(requisicoesPropostas);
        _dbContext.MockDbSet(motivoOcorrencias);
        _dbContext.MockDbSet(requisicoesOcorrencias);
        _dbContext.MockDbSet(requisicaoEstorno);
        _dbContext.MockDbSet(requisicaoJustificativa);
    }
}
