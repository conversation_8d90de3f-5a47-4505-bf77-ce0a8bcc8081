$(function () {
    const service = tRF3.sISPREC.analiseCpfCnpj.analiseCpfCnpj;

    let requisicoesDataTable = inicializarRequisicoesDataTable();

    const partesDataTable = inicializarPartesDataTable();

    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'An<PERSON>', 'Mes');

    $('#btnPesquisar').on('click', function (event) {
        event.preventDefault();
        novaPesquisa();
    });

    $('#btnDetalhesDaRequisicao').on('click', function (event) {
        let numProtocRequis = obterRequisicaoEmAnalise();

        if (numProtocRequis)
            window.open('ViewRequisicoes/DetalhesRequisicao?numProtocRequis=' + numProtocRequis);
        else
            abp.notify.error('Selecione uma requisição para visualizar os detalhes.');
    });

    $('#btnConsultarRfb').on('click', function (event) {
        let numeroRequisicao = obterRequisicaoEmAnalise();
        let cpfCnpj = obterParteEmAnalise();
        service.getDados(numeroRequisicao, cpfCnpj)
            .then(function (result) {
                if (result != null) {
                    if (cpfCnpj.length === 11) {
                        window.open("https://servicos.receita.fazenda.gov.br/servicos/cpf/consultasituacao/ConsultaPublica.asp?cpf=" + cpfCnpj + "&nascimento=" + result.dataNascimentoReceitaFormatado, "_blank");
                    }
                    else if (cpfCnpj.length === 14) {
                        window.open("https://solucoes.receita.fazenda.gov.br/Servicos/cnpjreva/cnpjreva_solicitacao.asp?cnpj=" + cpfCnpj, "_blank");
                    }
                }
            });
    });

    $('#ViewModel_NomeReceita').on('input', function () {
        service.compararNomes($('#ViewModel_NomeReceita').val().trim(), $('#ViewModel_NomeSistema').val().trim())
            .then(function (result) {
                if (result) {
                    $('#ViewModel_NomeReceita, #ViewModel_NomeSistema').removeClass('inputVermelho').addClass('inputVerde');
                } else {
                    $('#ViewModel_NomeReceita, #ViewModel_NomeSistema').removeClass('inputVerde').addClass('inputVermelho');
                }
            });
    });

    $('#btnOcultarSecaoTopo').on('click', function () {
        $.fn.dataTable.tables({ "filter": '.lista-dados', "api": true }).columns.adjust();
    });

    $(document).on('cadastroJustificativa:updated', function () {
        abp.notify.success('Salvo com sucesso!');
        novaPesquisa();
    });

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function (event, jqXHR) {
        // Garante que a UI será desbloqueada
        abp.ui.unblock();

        resetRequisicoesDataTable();
    });

    function inicializarRequisicoesDataTable() {
        return $('#CpfCnpjRequisicoesTable')
            // Bloqueia a tela antes de enviar a requisição ajax.
            .on('preXhr.dt', function () { abp.ui.block({ elm: 'body', busy: false }); })
            // Intercepta o evento após a requisição (sucesso ou erro) --> só está disparando após sucesso. Talvez o abp.libs.datatables.createAjax() esteja "tratando" o erro?
            .on('xhr.dt', function () { abp.ui.unblock(); })
            // Desbloqueia a tela em caso de erro (ex: timeout).
            .on('error.dt', function () { abp.ui.unblock(); })
            // Configura o datatable.
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                processing: true,
                serverSide: true,
                deferLoading: 0, // Delay the loading of server-side data until second draw.
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'numeroProtocoloRequisicao',
                ajax: abp.libs.datatables.createAjax(service.getRequisicoes, obterFiltrosPesquisa),
                columnDefs: [
                    {
                        title: "Requisição",
                        data: "numeroProtocoloRequisicao"
                    },
                    {
                        title: "Data de Protocolo",
                        data: "dataHoraProtocoloRequisicaoFormatado"
                    }
                ],
                drawCallback: function (settings) {
                    let tabela = this.api();
                    // Seleciona a primeira linha.
                    selecionarLinhaTabela(tabela, 0);
                }
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarRequisicaoEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="CpfCnpjRequisicoesTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="CpfCnpjRequisicoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function inicializarPartesDataTable() {
        return $('#PartesTable')
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'cpfCnpj',
                columnDefs: [
                    {
                        title: "CPF/CNPJ da parte",
                        data: "cpfCnpj"
                    },
                    {
                        title: "Nome da parte",
                        data: "nome"
                    }
                ]
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarParteEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="PartesTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="PartesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function obterFiltrosPesquisa() {
        const input = {};
        $('#AnaliseCpfCnpjFilterInput')
            .serializeArray()
            .forEach(function (data) {
                if (data.value !== '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnaliseCpfCnpjFilterInput./g, ''))] = data.value;
                }
            });
        return input;
    };

    function obterRequisicaoEmAnalise() { return requisicoesDataTable.row({ selected: true }).id(); };

    function obterParteEmAnalise() { return partesDataTable.row({ selected: true }).id(); };

    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        // Recarrega a tabela de requisições.
        requisicoesDataTable.ajax.reload();
    }

    function atualizarRequisicaoEmAnalise() {
        limparRequisicaoEmAnalise();

        let requisicaoEmAnalise = obterRequisicaoEmAnalise();

        if (requisicaoEmAnalise) {
            $('#ViewModel_NumeroDaRequisicao').val(requisicaoEmAnalise);

            $('.barra-navegacao button').prop('disabled', false);

            // Recarrega a tabela de partes com divergência.
            abp.ui.block({ elm: 'body', busy: true })

            service.getPartes(requisicaoEmAnalise)
                .then(partes => {
                    if (partes.totalCount == 0) {
                        abp.notify.error('Falha para obter as partes com divergência.');
                        limparParteEmAnalise();
                    }
                    else {
                        partesDataTable.clear().rows.add(partes.items).draw();
                        // Seleciona a primeira linha.
                        selecionarLinhaTabela(partesDataTable, 0);
                    }
                })
                .catch(() => {
                    abp.notify.error('Falha para obter as partes com divergência.');
                    limparParteEmAnalise();
                })
                .always(() => {
                    abp.ui.unblock();
                });
        }
    }

    function atualizarParteEmAnalise() {
        let parteEmAnalise = obterParteEmAnalise();

        if (parteEmAnalise) {
            $('#ViewModel_CpfCnpjParteAnalisada').val(parteEmAnalise);

            obterDadosRequisicao();
        }
        else {
            limparParteEmAnalise();
        }
    }

    function obterDadosRequisicao() {
        let requisicaoEmAnalise = obterRequisicaoEmAnalise();
        let parteEmAnalise = obterParteEmAnalise();

        abp.ui.block({ elm: 'body', busy: true });

        service.getDados(requisicaoEmAnalise, parteEmAnalise)
            .then(dados => {
                if (dados != null) {
                    limparDadosRequisicao();

                    $('#ViewModel_NomeSistema').val(dados.nome);
                    $('#ViewModel_NomeConselho').val(dados.nomeCjf);
                    $('#ViewModel_SituacaoCadastral').val(dados.situacaoCadastral);

                    let classe = dados.situacaoCadastral == 'REGULAR' || dados.situacaoCadastral == 'ATIVA' ? 'inputVerde' : 'inputVermelho';
                    if (dados.situacaoCadastral)
                        $('#ViewModel_SituacaoCadastral').addClass(classe);

                    $('#ViewModel_NomeJuizo').val(dados.juizo);
                    $('#ViewModel_Alvara').prop('checked', dados.alvara === true);
                    $('#ViewModel_Observacao').val(dados.observacao);
                }
            })
            .catch(() => {
                abp.notify.error('Falha para obter dados da requisição.');
                limparDadosRequisicao();
            })
            .always(() => {
                abp.ui.unblock();
            });
    }

    function limparRequisicaoEmAnalise() {
        $('#ViewModel_NumeroDaRequisicao').val('');
        $('.barra-navegacao button').prop('disabled', true);

        limparParteEmAnalise();
    }

    function limparParteEmAnalise() {
        $('#ViewModel_CpfCnpjParteAnalisada').val('');
        $('.botao-navegacao-tabela[data-tabela="PartesTable"]').prop('disabled', true);

        partesDataTable.clear().draw();
        limparDadosRequisicao();
    }

    function limparDadosRequisicao() {
        $('#ViewModel_NomeSistema').val('');
        $('#ViewModel_NomeConselho').val('');
        $('#ViewModel_NomeReceita').val('');
        $('#ViewModel_SituacaoCadastral').val('');
        $('#ViewModel_NomeJuizo').val('');
        $('#ViewModel_Alvara').val('');
        $('#ViewModel_Observacao').val('');
        $('.inputVerde').removeClass('inputVerde')
        $('.inputVermelho').removeClass('inputVermelho');
    }

    function resetRequisicoesDataTable() {
        requisicoesDataTable.destroy();
        $('#CpfCnpjRequisicoesTable').empty();

        limparRequisicaoEmAnalise();
        requisicoesDataTable = inicializarRequisicoesDataTable();
    }
});
