using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.AdvogadosJudiciais;

public class IndexModel : SISPRECPageModel
{
    public AdvogadoJudicialFilterInput AdvogadoJudicialFilter { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }
}

public class AdvogadoJudicialFilterInput
{
    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "ID Advogado")]
    [Range(0, int.MaxValue, ErrorMessage = "O ID Advogado deve estar entre {1} e {2}.")]
    public int? AdvogadoJudicialId { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Nome")]
    [MaxLength(220, ErrorMessage = "O nome deve ter no máximo {1} caracteres")]
    public string? Nome { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "CPF")]
    [MaxLength(11, ErrorMessage = "O CPF deve ter no máximo {1} caracteres")]
    public string? Cpf { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Código OAB")]
    [MaxLength(9, ErrorMessage = "O Código OAB deve ter no máximo {1} caracteres")]
    public string? CodigoOab { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Ativo")]
    public ESimNao? Ativo { get; set; }

}
