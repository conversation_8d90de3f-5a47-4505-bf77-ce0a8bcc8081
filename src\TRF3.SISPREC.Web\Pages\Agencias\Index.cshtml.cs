using Microsoft.AspNetCore.Mvc.Rendering;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Bancos;
using TRF3.SISPREC.Bancos.Dtos;
using TRF3.SISPREC.Enderecos;
using TRF3.SISPREC.Enums;
using Volo.Abp.Application.Dtos;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.Agencias.Agencia;

public class IndexModel : SISPRECPageModel
{
    private readonly IBancoAppService _bancoAppService;
    private readonly IEnderecoAppService _enderecoAppService;

    public AgenciaFilterInput AgenciaFilter { get; set; } = new();

    public IndexModel(IBancoAppService bancoAppService, IEnderecoAppService enderecoAppService)
    {
        _bancoAppService = bancoAppService;
        _enderecoAppService = enderecoAppService;
    }

    public virtual async Task OnGetAsync()
    {
        PagedResultDto<BancoDto> pagedResultDto = await _bancoAppService.GetListAsync(new BancoGetListInput());

        AgenciaFilter = new();
        AgenciaFilter.BancoLookupList.AddRange(pagedResultDto.Items.Select(x => new SelectListItem(x.NomeBanco, x.BancoId.ToString())).ToList());

        await Task.CompletedTask;
    }

    public class AgenciaFilterInput
    {

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Número Agência")]
        public int? AgenciaId { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Nome Agência")]
        public string? NomeAgencia { get; set; }

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Banco")]
        [SelectItems(nameof(BancoLookupList))]
        public int? BancoId { get; set; }
        public List<SelectListItem> BancoLookupList { get; set; } = new List<SelectListItem>
        {
            new SelectListItem(string.Empty, null)
        };

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Município")]
        [SelectItems(nameof(CidadeLookupList))]
        public int? MunicipioId { get; set; }
        public List<SelectListItem> CidadeLookupList { get; set; } = new List<SelectListItem>
        {
            new SelectListItem(string.Empty, null)
        };

        [FormControlSize(AbpFormControlSize.Small)]
        [Display(Name = "Ativo")]
        public ESimNao? Ativo { get; set; }
    }
}
