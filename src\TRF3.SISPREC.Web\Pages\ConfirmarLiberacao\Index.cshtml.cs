using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Web.Pages.ConfirmarLiberacao.ViewModels;
using TRF3.SISPREC.Web.Pages.FilterInputs;

namespace TRF3.SISPREC.Web.Pages.ConfirmarLiberacao
{

    [ExcludeFromCodeCoverage]
    public class IndexModel : SISPRECPageModel
    {
        public ConfirmarLiberacaoViewModel? ViewModel { get; set; } = new();

        public AnaliseFilterInput? ConfirmarLiberacaoFilterInput { get; set; }

        public async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}
