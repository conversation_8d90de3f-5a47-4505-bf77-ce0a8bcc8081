using TRF3.SISPREC.AnalisePendencias;
using TRF3.SISPREC.Enums;
using Volo.Abp.DependencyInjection;

namespace TRF3.SISPREC.ConfirmarLiberacoes
{
    public interface IConfirmarLiberacaoRepository : ITransientDependency
    {
        Task<List<RequisicoesPendentes>> BuscarRequisicoesPendentesParaLiberacao(
            ETipoProcedimentoRequisicao? tipoProcedimento,
            int? ano,
            int? mes,
            DateTime? dataInicio,
            DateTime? dataTermino,
            string? numeroRequisicao);

        Task<DadosBasicoParaConfirmarLiberacao?> BuscaPorInformacaoBasicaRequisicoes(string numeroRequisicao);

        Task<List<DadosOcorrenciasParaConfirmarLiberacao>> BuscaPorOcorrenciasRequisicoes(string numeroRequisicao);

        Task<List<JustificativasAnalises>> BuscaPorJustificativasAnalises(string numeroRequisicao);
    }
}
