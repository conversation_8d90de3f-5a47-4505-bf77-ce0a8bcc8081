@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.ConferirObservacao
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Análise do Campo Observação";
    PageLayout.Content.MenuItemName = SISPRECMenus.ConferirObservacao;
}

@section scripts
{
    <abp-script src="/js/util.js" />
    <abp-script src="/js/exportacao-pdf.js" />
    <abp-script src="/Pages/ConferirObservacao/index.js" />
    <abp-script src="/js/exportacao-excel.js" />
    <abp-script src="/js/componente-utils.js" />    
}
@section styles
{
    <abp-style src="/Pages/ConferirObservacao/index.css" />
}

<abp-card>
    <abp-card-body>
        <form asp-for="ConsultaFilter" id="ConsultaFilter" column-size="_2">
            <abp-row>
                <abp-column>
                    <abp-select asp-for="ConsultaFilter.TipoProcedimento" />
                </abp-column>

                <abp-column>
                    <abp-input asp-for="ConsultaFilter.Ano" />
                </abp-column>

                <abp-column>
                    <abp-select asp-for="ConsultaFilter.Mes" />
                </abp-column>

                <abp-column>
                    <abp-select asp-for="ConsultaFilter.PossuiJustificativa" />
                </abp-column>
                <abp-column>
                    <input-requisicao-pesquisa asp-for="ConsultaFilter.NumeroRequisicao" />
                </abp-column>
            </abp-row>
        </form>

        <div>
            <input type="checkbox" id="selecionarTodos">
            <label for="selecionarTodos">Filtro por termos (escolha dentre as opções abaixo e/ou selecione "Outros" para digitar livremente)</label>
        </div>
        <div class="checkbox-container" style="display: flex; flex-wrap: wrap; gap: 6px;">
            @foreach (var opcao in new List<string> { "ALVAR", "BLOQ", "COLOC", "CONTRAT", "CONVER", "CUIDAD", "DEPOS", "DETERM", "DISP", "FAVOR", "HONOR", "LEVANT", "ORDEM", "PENHOR", "PODER", "REFER", "SAQUE" })
            {
                <div class="d-flex align-items-center" style="margin: 2px;">
                    @Html.CheckBox($"OpcoesCheckbox[{opcao}]", Model.ConsultaFilter.OpcoesCheckbox.Contains(opcao), new { id = "checkBox_" + opcao })
                    <label class="ms-1" for="checkBox_@opcao">@opcao</label>
                </div>
            }
            <div class="d-flex align-items-center" style="margin: 2px; flex-basis: 100%;">
                @Html.CheckBox("OpcoesCheckbox[Outros]", Model.ConsultaFilter.OpcoesCheckbox.Contains("Outros"), new { id = "checkBox_OUTROS" })
                <label class="ms-1" for="checkBox_OUTROS">Outros</label>
            </div>
            <div style="margin: 2px; flex-basis: 100%;">
                <input type="text" id="input_outros" class="form-control" placeholder="Digite os termos separados por vírgula (ABC, DEF, GHI JK...)" value="@Model.ConsultaFilter.OutrosTermos" disabled />
            </div>
        </div>
        <abp-column class="d-flex align-items-start">
            <abp-button size="Small" class="mx-0" button-type="Primary" id="btnPesquisar">Pesquisar</abp-button>
            <abp-button block="true" size="Small" id="exporta-excel" class="btn-primary-outline custom-border mx-1">
                Exportar Excel
            </abp-button>
            <abp-button size="Small" id="btnExportarPDF" class="btn-primary-outline custom-border mx">
                Exportar PDF
            </abp-button>
        </abp-column>
        <hr />
        <abp-table striped-rows="true" id="ConferirObservacaoTable" class="nowrap" />
        <abp-row class="mt-3">
            <abp-column class="text-start">
                <abp-button id="marcar-todos" text="Marcar/Desmarcar Todos" button-type="Outline_Primary" size="Medium" />
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="btnCadastrarJustificativa" text="Cadastrar Justificativa" data-tipo-analise="5" button-type="Outline_Primary" size="Medium" />
            </abp-column>
        </abp-row>
    </abp-card-body>
</abp-card>
