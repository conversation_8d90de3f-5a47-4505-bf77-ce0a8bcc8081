@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.OcorrenciaMotivos
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Motivo Ocorrência";
    PageLayout.Content.BreadCrumb.Add("OcorrenciaMotivos");
    PageLayout.Content.MenuItemName = SISPRECMenus.OcorrenciaMotivo;
}

@section scripts
{
    <abp-script src="/js/exportacao-pdf.js" />
    <abp-script src="/Pages/OcorrenciaMotivos/index.js" />
}
@section styles
{
    <abp-style src="/Pages/OcorrenciaMotivos/index.css" />
}

<abp-card>
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="OcorrenciaMotivoCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="NewOcorrenciaMotivoButton"
                            text="Novo"
                            icon="plus"
                            button-type="Primary" />
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <abp-collapse-body id="OcorrenciaMotivoCollapse">
            <form method="get" class="mb-3" id="OcorrenciaMotivoFilter">
                <abp-row>
                    <abp-column size="_2">
                        <abp-input asp-for="OcorrenciaMotivoFilter.CodigoMotivo" />
                    </abp-column>
                    <abp-column size="_3">
                        <abp-input asp-for="OcorrenciaMotivoFilter.DescricaoMotivo" />
                    </abp-column>
                    <abp-column size="_3">
                        <abp-select asp-for="OcorrenciaMotivoFilter.AcaoTipoId" asp-items="Model.OcorrenciaMotivoFilter.AcaoTipoLookupList" />
                    </abp-column>
                    <abp-column size="_2">
                        <abp-select asp-for="OcorrenciaMotivoFilter.AnaliseTelaId" asp-items="Model.OcorrenciaMotivoFilter.AnaliseTelaLookupList" />
                    </abp-column>
                    <abp-column size="_2">
                        <abp-select asp-for="OcorrenciaMotivoFilter.Ativo" />
                    </abp-column>
                </abp-row>
            </form>
        </abp-collapse-body>
        <abp-table striped-rows="true" id="OcorrenciaMotivoTable" class="nowrap" />
    </abp-card-body>
</abp-card>
