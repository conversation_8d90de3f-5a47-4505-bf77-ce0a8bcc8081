using Microsoft.EntityFrameworkCore;
using System.Linq.Dynamic.Core;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.AcoesJustificativa;
using TRF3.SISPREC.AnalisePendencias;
using TRF3.SISPREC.AnaliseTelas;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.OcorrenciaMotivos;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicaoEstornos;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.RequisicoesVerificacoes;
using TRF3.SISPREC.SituacoesRequisicoesProtocolos;
using Volo.Abp.EntityFrameworkCore;

namespace TRF3.SISPREC.ConfirmarLiberacoes
{
    public class ConfirmarLiberacaoRepository : IConfirmarLiberacaoRepository
    {
        private readonly IDbContextProvider<SISPRECDbContext> _dbContextProvider;

        public ConfirmarLiberacaoRepository(IDbContextProvider<SISPRECDbContext> dbContextProvider)
        {
            _dbContextProvider = dbContextProvider;
        }

        public async Task<List<RequisicoesPendentes>> BuscarRequisicoesPendentesParaLiberacao(
            ETipoProcedimentoRequisicao? tipoProcedimento,
            int? ano,
            int? mes,
            DateTime? dataInicio,
            DateTime? dataTermino,
            string? numeroRequisicao)
        {
            var context = await _dbContextProvider.GetDbContextAsync();
            var query =
                from rpc in context.Set<RequisicaoProtocolo>().AsNoTracking()
                join rrp in context.Set<RequisicaoProposta>().AsNoTracking() on rpc.NumeroProtocoloRequisicao equals rrp.NumeroProtocoloRequisicao
                join pro in context.Set<Proposta>().AsNoTracking() on rrp.PropostaId equals pro.PropostaId
                where
                  rpc.SituacaoRequisicaoId == (int)ESituacaoRequisicao.PENDENTE
                  && rrp.SituacaoRequisicaoProposta != ESituacaoRequisicaoProposta.CANCELADO

                  // Não pode haver justificativas diferentes de "LIBERAÇÃO"
                  && !context.Set<RequisicaoJustificativa>()
                      .Join(context.Set<AcaoJustificativa>(),
                            rj => rj.AcaoJustificativaId,
                            aj => aj.AcaoJustificativaId,
                            (rj, aj) => new { rj.NumeroProtocoloRequisicao, aj.AcaoTipoId })
                      .Any(j => j.AcaoTipoId != (int)EDescricaoAcaoTipo.LIBERACAO
                                && j.NumeroProtocoloRequisicao == rpc.NumeroProtocoloRequisicao)

                // Não pode haver pendência não analisada
                && !context.Set<RequisicaoOcorrencia>()
                    .Join(context.Set<OcorrenciaMotivo>(),
                          oco => oco.CodigoMotivoOcorrenciaId,
                          ocm => ocm.OcorrenciaMotivoId,
                          (oco, ocm) => new { oco.NumeroProtocoloRequisicaoId, ocm.AnaliseTelaId })
                    .Where(o => (o.AnaliseTelaId == Convert.ToInt32(EDescricaoAnaliseTela.CPF_CNPJ) ||
                                 o.AnaliseTelaId == Convert.ToInt32(EDescricaoAnaliseTela.PENDENCIAS) ||
                                 o.AnaliseTelaId == Convert.ToInt32(EDescricaoAnaliseTela.PREVENCAO) ||
                                 o.AnaliseTelaId == Convert.ToInt32(EDescricaoAnaliseTela.ORGAO_PSS) ||
                                 o.AnaliseTelaId == null) &&
                                o.NumeroProtocoloRequisicaoId == rpc.NumeroProtocoloRequisicao)
                    .Any(o => !context.Set<RequisicaoJustificativa>()
                                      .Any(rj => rj.NumeroProtocoloRequisicao == o.NumeroProtocoloRequisicaoId &&
                                                 (rj.AnaliseTelaId == o.AnaliseTelaId ||
                                                  (o.AnaliseTelaId == null && rj.AnaliseTelaId == Convert.ToInt32(EDescricaoAnaliseTela.PENDENCIAS)))))

                 // Não pode aparecer para Confirmar Liberação se for requisição de reinclusão não analisada.
                 && !context.Set<RequisicaoProtocolo>()
                    .Join(context.Set<RequisicaoEstorno>(),
                          rpc2 => rpc2.NumeroProtocoloRequisicao,
                          est => est.NumeroProtocoloRequisicaoId,
                          (rpc2, est) => new { rpc2, est })
                    .Where(x => x.rpc2.IndicadorInclusaoRequisicao == EIndicadorInclusaoRequisicao.REINCLUSAO) // Enum direto
                    .Where(x => x.rpc2.NumeroProtocoloRequisicao == rpc.NumeroProtocoloRequisicao) // Filtro pelo protocolo
                    .Any(x => !context.Set<RequisicaoJustificativa>()
                                      .Any(rj => rj.NumeroProtocoloRequisicao == x.rpc2.NumeroProtocoloRequisicao
                                                 && rj.AnaliseTelaId == Convert.ToInt32(EDescricaoAnaliseTela.REINCLUSAO))) // Verifica se não existe justificativa

                // Não pode aparecer para Confirmar Liberação se houver verificação de requisição não executada.
                && !context.Set<RequisicaoVerificacao>()
                    .Any(rv => !rv.Executado
                               && rv.NumeroProtocoloRequisicaoId == rpc.NumeroProtocoloRequisicao)

                select new { rpc, pro };

            if (!string.IsNullOrEmpty(numeroRequisicao))
                query = query.Where(req => req.rpc.NumeroProtocoloRequisicao == numeroRequisicao);
            else
            {
                query = query
                    .Where(req => req.pro.TipoProcedimentoId == tipoProcedimento.ToString() && req.pro.AnoProposta == ano && req.pro.MesProposta == mes)
                    .WhereIf(dataInicio.HasValue, req => req.rpc.DataHoraProtocoloRequisicao >= dataInicio)
                    .WhereIf(dataTermino.HasValue, req => req.rpc.DataHoraProtocoloRequisicao <= dataTermino!.Value.Date.AddDays(1).AddTicks(-1));
            }

            return await query
                .Select(q => new RequisicoesPendentes { NumeroProtocoloRequisicaoPendente = q.rpc.NumeroProtocoloRequisicao })
                .Distinct()
                .ToListAsync();
        }

        public async Task<DadosBasicoParaConfirmarLiberacao?> BuscaPorInformacaoBasicaRequisicoes(string numeroRequisicao)
        {
            var context = await _dbContextProvider.GetDbContextAsync();

            var query =
                from rpc in context.Set<RequisicaoProtocolo>().AsNoTracking()
                join rrp in context.Set<RequisicaoProposta>().AsNoTracking() on rpc.NumeroProtocoloRequisicao equals rrp.NumeroProtocoloRequisicao
                join pro in context.Set<Proposta>().AsNoTracking() on rrp.PropostaId equals pro.PropostaId
                join sit in context.Set<SituacaoRequisicaoProtocolo>().AsNoTracking() on rpc.SituacaoRequisicaoId equals sit.SituacaoRequisicaoProtocoloId
                where rpc.NumeroProtocoloRequisicao == numeroRequisicao
                orderby rpc.NumeroProtocoloRequisicao
                select new DadosBasicoParaConfirmarLiberacao
                {
                    NumeroProtocoloRequisicao = rpc.NumeroProtocoloRequisicao,
                    TipoProcedimento = pro.TipoProcedimentoId,
                    AnoPropos = pro.AnoProposta,
                    MesPropos = pro.MesProposta,
                    SituacaoProposta = sit.DescricaoSituacao,
                    SituacaoRequisicao = rrp.SituacaoRequisicaoProposta.ToString()
                };

            return await query.FirstOrDefaultAsync();
        }

        public async Task<List<DadosOcorrenciasParaConfirmarLiberacao>> BuscaPorOcorrenciasRequisicoes(string numeroRequisicao)
        {
            var context = await _dbContextProvider.GetDbContextAsync();
            var query =
                await (
                    from rpc in context.Set<RequisicaoProtocolo>().AsNoTracking()
                    join oco in context.Set<RequisicaoOcorrencia>().AsNoTracking() on rpc.NumeroProtocoloRequisicao equals oco.NumeroProtocoloRequisicaoId
                    join ocm in context.Set<OcorrenciaMotivo>().AsNoTracking() on oco.CodigoMotivoOcorrenciaId equals ocm.OcorrenciaMotivoId
                    join at in context.Set<AnaliseTela>().AsNoTracking() on ocm.AnaliseTelaId equals at.AnaliseTelaId into atJoin
                    from at in atJoin.DefaultIfEmpty()
                    where rpc.NumeroProtocoloRequisicao == numeroRequisicao
                    select new DadosOcorrenciasParaConfirmarLiberacao
                    {
                        TipoOcorrencia = ocm.AnaliseTelaId != null ? at.Descricao.GetEnumDescription() : EDescricaoAnaliseTela.PENDENCIAS.GetEnumDescription(),
                        Descricao = ocm.DescricaoMotivo,
                        DataOCorrencia = oco.DataOcorrencia
                    }
                ).ToListAsync();

            return query
                .OrderBy(x => x.TipoOcorrencia)
                .ThenByDescending(x => x.DataOCorrencia)
                .ThenBy(x => x.Descricao)
                .ToList();
        }

        public async Task<List<JustificativasAnalises>> BuscaPorJustificativasAnalises(string numeroRequisicao)
        {
            var context = await _dbContextProvider.GetDbContextAsync();
            var query =
                from rpc in context.Set<RequisicaoProtocolo>().AsNoTracking()
                join rj in context.Set<RequisicaoJustificativa>().AsNoTracking() on rpc.NumeroProtocoloRequisicao equals rj.NumeroProtocoloRequisicao
                join aj in context.Set<AcaoJustificativa>().AsNoTracking() on rj.AcaoJustificativaId equals aj.AcaoJustificativaId
                join ati in context.Set<AcaoTipo>().AsNoTracking() on aj.AcaoTipoId equals ati.AcaoTipoId
                join at in context.Set<AnaliseTela>().AsNoTracking() on rj.AnaliseTelaId equals at.AnaliseTelaId into atJoin
                from at in atJoin.DefaultIfEmpty() // LEFT JOIN
                where rpc.NumeroProtocoloRequisicao == numeroRequisicao
                orderby rj.DataAnalise descending
                select new JustificativasAnalises
                {
                    DataAnalise = rj.DataAnalise,
                    ComplementoJustificativa = rj.ComplementoMotivo ?? string.Empty,
                    TipoAcao = ati.Descricao.GetEnumDescription(),
                    DescricaoJustificativa = aj.Descricao,
                    DescricaoTela = at.Descricao.GetEnumDescription(),
                    UsuarioAnalise = rj.NomeUsuario
                };

            return await query.ToListAsync();
        }
    }
}
