$(function () {

    $("#btnPesquisar").click(function (event) {
        event.preventDefault();
        dataTable.ajax.reload();
    });

    const getFilter = function () {
        const input = {};
        $("#ExpedienteAdministrativoFilter")
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/ExpedienteAdministrativoFilter./g, ''))] = data.value;
                }
            })
        return input;
    };

    const service = tRF3.sISPREC.expedientesAdministrativo.expedienteAdministrativo;

    $('#NewExpedienteAdministrativoButton').click(function (e) {
        e.preventDefault();
        window.location = abp.appPath + 'ExpedientesAdministrativos/CreateExpediente';
    });
    const dataTable = $('#ExpedienteAdministrativoTable').DataTable(abp.libs.datatables.normalizeConfiguration({
        processing: true,
        serverSide: true,
        paging: true,
        searching: false,//disable default searchbox
        autoWidth: false,
        scrollCollapse: true,
        order: [
            [3, 'desc'],
            [1, 'desc']
        ],
        ajax: abp.libs.datatables.createAjax(service.getExpedientes, getFilter),
        columnDefs: [
            {
                rowAction: {
                    items:
                        [
                            {
                                text: "Alterar",
                                action: function (data) {
                                    abp.notify.error("Funcionalidade não implementada")
                                }
                            },
                            {
                                text: "Excluir",
                                action: function (data) {
                                    abp.notify.error("Funcionalidade não implementada")
                                }
                            },
                            {
                                text: "Visualizar Documentos",
                                action: function (data) {
                                    abp.notify.error("Funcionalidade não implementada")
                                }
                            },
                        ]
                }
            },
            {
                title: "Nº Expediente",
                data: "numeroProcessoSei",
            },
            {
                title: "Tipo",
                data: "tipoExpedienteAdministrativo"
            },
            {
                title: "Data",
                data: "dataExpedienteAdministrativo",
                render: DataTable.render.datetime('DD/MM/YYYY HH:mm:ss'),
            },
            {
                title: "Usuário",
                data: "nomeUsuario"
            },
            {
                title: "Motivo",
                data: "observacaoExpedienteAdministrativo",
            },
            {
                title: "Status",
                data: "statusExpedienteAdminstrativo",
            },
            {
                title: "Nº Bloco",
                data: "blocoSisprecId",
            },
        ]
    }));

});
