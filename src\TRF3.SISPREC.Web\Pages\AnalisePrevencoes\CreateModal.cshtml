@page
@using Microsoft.AspNetCore.Mvc.Localization
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@model TRF3.SISPREC.Web.Pages.AnalisePrevencoes.CreateModalModel
@{
    Layout = null;
}

<form data-ajaxForm="true" data-check-form-on-close="false" asp-page="CreateModal">
    <abp-modal>
        <abp-modal-header title="Inserir Observação"></abp-modal-header>
        <abp-modal-body>
            <abp-row>
                <abp-input class="custom-textarea" size="Large" rows="4" asp-for="ObservacaoGeracaoEspelho" />
                <input type="hidden" asp-for="Id" />
            </abp-row>
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Cancel|AbpModalButtons.Save)"></abp-modal-footer>
    </abp-modal>
</form>