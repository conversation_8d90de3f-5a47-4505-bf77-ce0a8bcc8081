$(function () {
    const service = tRF3.sISPREC.analiseReinclusoes.analiseReinclusao;

    let requisicoesDataTable = inicializarRequisicoesDataTable();

    const estornoDataTable = inicializarEstornoDataTable();

    const createModal = new abp.ModalManager(abp.appPath + 'AnalisePrevencoes/CreateModal');

    createModal.onResult(function () {
        let numeroRequisicao = obterRequisicaoOriginalEmAnalise();

        if (numeroRequisicao) {
            let observacaoEspelho = $('#ObservacaoGeracaoEspelho').val();
            window.listaExportacaoObservacao.set(numeroRequisicao, observacaoEspelho);
        }
    });

    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'Ano', 'Mes');

    $('#btnPesquisar').on('click', function (event) {
        event.preventDefault();
        novaPesquisa();
    });

    $('#btnSalvarComparada').on('click', function (e) {
        e.preventDefault();
        let numeroRequisicao = obterRequisicaoOriginalEmAnalise();

        if (numeroRequisicao) {
            let observacaoEspelho = window.listaExportacaoObservacao.get(numeroRequisicao);

            createModal.open();
            setTimeout(() => {
                let modal = document.querySelector(".modal"); // Ajuste para a classe/campo do modal
                let inputElement = modal?.querySelector(".custom-textarea"); // Busca dentro do modal

                if (inputElement) {
                    inputElement.value = observacaoEspelho ?? '';
                    inputElement.dispatchEvent(new Event("input")); // Se for um componente reativo
                }
            }, 300);
        }
    });

    $('#btnOcultarSecaoTopo').on('click', function () {
        $.fn.dataTable.tables({ "filter": '.lista-dados', "api": true }).columns.adjust();
    });

    $(document).on('cadastroJustificativa:updated', function () {
        abp.notify.success('Salvo com sucesso!');
        novaPesquisa();
    });

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function (event, jqXHR) {
        // Garante que a UI será desbloqueada
        abp.ui.unblock();

        resetRequisicoesDataTable();
    });

    function inicializarRequisicoesDataTable() {
        return $('#ReinclusaoRequisicoesTable')
            // Bloqueia a tela antes de enviar a requisição ajax.
            .on('preXhr.dt', function () { abp.ui.block({ elm: 'body', busy: false }); })
            // Intercepta o evento após a requisição (sucesso ou erro) --> só está disparando após sucesso. Talvez o abp.libs.datatables.createAjax() esteja "tratando" o erro?
            .on('xhr.dt', function () { abp.ui.unblock(); })
            // Desbloqueia a tela em caso de erro (ex: timeout).
            .on('error.dt', function () { abp.ui.unblock(); })
            // Configura o datatable.
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                processing: true,
                serverSide: true,
                deferLoading: 0, // Delay the loading of server-side data until second draw.
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'numeroProtocoloRequisicao',
                ajax: abp.libs.datatables.createAjax(service.obterAnaliseReinclusao, obterFiltrosPesquisa),
                columnDefs: [
                    {
                        title: "Requisição",
                        data: "numeroProtocoloRequisicao"
                    },
                    {
                        title: "Requisição Original",
                        data: "numeroRequisicaoOriginal"
                    }
                ],
                drawCallback: function (settings) {
                    let tabela = this.api();
                    // Seleciona a primeira linha.
                    selecionarLinhaTabela(tabela, 0);
                }
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarRequisicaoEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="ReinclusaoRequisicoesTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="ReinclusaoRequisicoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function inicializarEstornoDataTable() {
        return $('#EstornoTable').DataTable(abp.libs.datatables.normalizeConfiguration({
            paging: false,          // Remove a paginação
            searching: false,       // Remove a barra de busca
            info: false,            // Remove o texto "Mostrando X de Y registros"
            ordering: false,        // Remove a ordenação do cabeçalho
            createdRow: function (row) {
                $(row).addClass('requisicao-principal');
                $('td', row).addClass('text-start');
            },
            columnDefs: [
                {
                    data: "cpfCnpj", title: "CPF/CNPJ",
                    createdCell: function (td, cellData, rowData, row, col) {
                        $(td).attr('name', 'tdCpfCnpjBeneficiario');
                    }
                },
                {
                    data: "nome", title: "Beneficiário",
                    createdCell: function (td, cellData, rowData, row, col) {
                        $(td).attr('name', 'tdNomeBeneficiario');
                    }
                },
                {
                    data: "conta", title: "Número da conta corrente",
                    createdCell: function (td, cellData, rowData, row, col) {
                        $(td).attr('name', 'tdContaCnpjBeneficiario');
                    }
                },
                {
                    data: "valorFormatado", title: "Valor",
                    createdCell: function (td, cellData, rowData, row, col) {
                        $(td).attr('name', 'tdValorFormatadoCnpjBeneficiario');
                    }
                },
                {
                    data: "dataContaFormatado", title: "Data Conta",
                    createdCell: function (td, cellData, rowData, row, col) {
                        $(td).attr('name', 'tdDataContaFormatadoCnpjBeneficiario');
                    }
                }
            ]
        }));
    }

    function obterFiltrosPesquisa() {
        const input = {};
        $('#AnaliseReinclusaoFilterInput')
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnaliseReinclusaoFilterInput./g, ''))] = data.value;
                }
            })
        return input;
    };

    function obterRequisicaoEmAnalise() { return requisicoesDataTable.row({ selected: true }).id(); };

    function obterRequisicaoOriginalEmAnalise() {
        const rowData = requisicoesDataTable.row({ selected: true }).data();
        return rowData ? rowData.numeroRequisicaoOriginal : null;
    };

    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        // Recarrega a tabela de requisições.
        requisicoesDataTable.ajax.reload();
    }

    function atualizarRequisicaoEmAnalise() {
        limparRequisicaoEmAnalise();

        let requisicaoEmAnalise = obterRequisicaoEmAnalise();
        let requisicaoOriginalEmAnalise = obterRequisicaoOriginalEmAnalise();

        if (requisicaoEmAnalise) {
            $('#ViewModel_NumeroDaRequisicao').val(requisicaoEmAnalise);
            $('#ViewModel_NumeroDaRequisicaoOriginal').val(requisicaoOriginalEmAnalise);

            $('.barra-navegacao button').prop('disabled', false);

            carregarEstorno();
        }
    }

    function carregarEstorno() {
        let numeroProtocoloRequisicao = obterRequisicaoEmAnalise();
        let numeroProtocoloRequisicaoOriginal = obterRequisicaoOriginalEmAnalise();

        if (numeroProtocoloRequisicao && numeroProtocoloRequisicaoOriginal) {
            service.getEstornoRequisicao(numeroProtocoloRequisicao, numeroProtocoloRequisicaoOriginal)
                .done(function (result) {
                    let data = [result]
                    estornoDataTable.clear().rows.add(data).draw();

                    carregarDadosRequisicao(numeroProtocoloRequisicao, true, true);
                    carregarDadosRequisicao(numeroProtocoloRequisicaoOriginal, false, true);
                })
                .fail(function () {
                    abp.notify.error('Falha para obter dados de estorno da requisição.');
                    limparDadosRequisicao(true);
                });
        }
        else {
            limparDadosRequisicao(true);
        }
    }

    function limparRequisicaoEmAnalise() {
        $('#ViewModel_NumeroDaRequisicao').val('');
        $('#ViewModel_NumeroDaRequisicaoOriginal').val('');
        $('.barra-navegacao button').prop('disabled', true);

        window.listaExportacaoObservacao = new Map();
        estornoDataTable.clear().draw();
        limparDadosRequisicao(true);
    }

    function resetRequisicoesDataTable() {
        requisicoesDataTable.destroy();
        $('#ReinclusaoRequisicoesTable').empty();

        limparRequisicaoEmAnalise();
        requisicoesDataTable = inicializarRequisicoesDataTable();
    }
});
