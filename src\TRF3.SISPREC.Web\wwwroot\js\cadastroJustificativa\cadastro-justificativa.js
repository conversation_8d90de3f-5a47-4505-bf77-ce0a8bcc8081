$(document).ready(function () {
    //Carrega component modal cadastro de justificativas.

    const cadastroJustificativaModal = new abp.ModalManager({
        viewUrl: abp.appPath + 'Components/CadastroJustificativa/CadastroJustificativaModal',
        modalClass: 'cadastroJustificativa'
    });

    // Dispara um evento personalizado quando o modal retorna um resultado
    cadastroJustificativaModal.onResult(function () {
        $(document).trigger('cadastroJustificativa:updated'); // Evento global
    });

    const service = tRF3.sISPREC.analiseTelaAppService.analiseTela;

    $('#btnCadastrarJustificativa').click(function (e) {
        e.preventDefault();

        const tipoAnalise = $('#btnCadastrarJustificativa').data('tipo-analise');

        if (!tipoAnalise) {
            abp.message.error('Erro ao carregar modal: tipo de análise não definido.');
            return;
        }

        let numeroRequisicao = $('#ViewModel_NumeroDaRequisicao').val();

        // Primeiro obtém o tipo de tela e depois processa o restante da lógica
        service.getEhAnaliseIndividual(tipoAnalise)
            .then(tipoTela => {
                return tipoTela ? processaAnaliseIndividual(numeroRequisicao) : processaAnaliseEmLote();
            })
            .then(({ arrayId, tipoProcedimento }) => {
                abrirModal(arrayId, tipoProcedimento, tipoAnalise);
            })
            .catch(error => {
                console.error("Erro:", error);
                retornaErros(error);
            });
    });

    function processaAnaliseIndividual(numeroRequisicao) {
        if (!numeroRequisicao) {
            return Promise.reject('Número da requisição não encontrado.');
        }

        return service.getProcedimento(numeroRequisicao)
            .then(procedimento => {
                if (!procedimento) {
                    return Promise.reject('Procedimento não encontrado');
                }

                return {
                    arrayId: procedimento.numeroProtocoloRequisicao,
                    tipoProcedimento: procedimento.tipoProcedimentoId
                };
            });
    }

    function processaAnaliseEmLote() {
        let arrayId = "";
        let primeiroId = null;

        $('.row-checkbox:checked').each(function () {
            const currentId = $(this).data('id');
            arrayId += `${currentId},`;

            if (!primeiroId) {
                primeiroId = currentId; // pega apenas o primeiro ID
            }
        });

        if (!arrayId) {
            return Promise.reject('Selecione ao menos uma requisição.');
        }

        arrayId = arrayId.slice(0, -1); // Remove a vírgula final

        if (!primeiroId) {
            return Promise.reject('ID do procedimento não encontrado.');
        }

        // Aqui usamos apenas o primeiro ID para buscar o tipoProcedimento
        return service.getProcedimento(primeiroId).then(procedimento => {
            if (!procedimento) {
                return Promise.reject('Dados do procedimento não encontrados');
            }

            let tipoProcedimento = procedimento.tipoProcedimentoId;

            return { arrayId, tipoProcedimento };
        });
    }


    function abrirModal(arrayId, tipoProcedimento, tipoAnalise) {
        cadastroJustificativaModal.open(
            {
                id: arrayId,
                procedimento: tipoProcedimento,
                tipoAnalise: tipoAnalise
            }, function () {
                if (typeof initModal === 'function') {
                    initModal();
                }
            }
        );
    }


    function retornaErros(error) {
        // Trata a mensagem de erro de forma apropriada
        if (error === 'Selecione ao menos uma requisição.') {
            abp.message.info(error);
        } else {
            abp.message.error('Erro ao carregar modal: ' + error);
        }
    }
});