using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.AcaoTipos;
using TRF3.SISPREC.AnaliseTelas;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.RequisicoesOcorrencias;

namespace TRF3.SISPREC.Web.Pages.OcorrenciaMotivos.ViewModels;

public class DetalheOcorrenciaMotivoViewModel
{
    [Display(Name = "OcorrenciaMotivoId")]
    public int OcorrenciaMotivoId { get; set; }

    [Display(Name = "Código Motivo")]
    public int CodigoMotivo { get; set; }

    [Display(Name = "Ação Tipo")]
    public int? AcaoTipoId { get; set; }

    [Display(Name = "Tipo Análise")]
    public int? AnaliseTelaId { get; set; }

    [Display(Name = "Descrição Motivo")]
    public string? DescricaoMotivo { get; set; }

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; }

    [Display(Name = "Deletado")]
    public bool IsDeleted { get; set; }

    [Display(Name = "")]
    public ICollection<RequisicaoOcorrencia>? RequisicaoOcorrencia { get; set; }

    [Display(Name = "")]
    public AcaoTipo? AcaoTipo { get; set; }

    [Display(Name = "")]
    public AnaliseTela? AnaliseTela { get; set; }

    [Display(Name = "Ação Tipo")]
    public string? AcaoTipoDescricao =>
        AcaoTipoId.HasValue ? ((EDescricaoAcaoTipo)AcaoTipoId.Value).GetEnumDescription() : null;

    [Display(Name = "Tipo Análise")]
    public string? AnaliseTelaDescricao =>
        AnaliseTelaId.HasValue ? ((EDescricaoAnaliseTela)AnaliseTelaId.Value).GetEnumDescription() : null;
}
