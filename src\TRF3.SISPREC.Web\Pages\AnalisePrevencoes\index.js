$(function () {
    const service = tRF3.sISPREC.analisePrevencoes.analisePrevencao;

    let requisicoesDataTable = inicializarRequisicoesDataTable();

    const prevencoesDataTable = inicializarPrevencoesDataTable();

    const createModal = new abp.ModalManager(abp.appPath + 'AnalisePrevencoes/CreateModal');

    createModal.onResult(function () {
        let prevencaoEmAnalise = obterPrevencaoEmAnalise();

        if (prevencaoEmAnalise && prevencaoEmAnalise.requisicaoAnterior) {
            let observacaoEspelho = $('#ObservacaoGeracaoEspelho').val();
            window.listaExportacaoObservacao.set(prevencaoEmAnalise.requisicaoAnterior, observacaoEspelho);
        }
    });

    configurarFiltroProcedimentoAnoMes('TipoProcedimento', 'Ano', 'Mes');

    $('#btnPesquisar').on('click', function (event) {
        event.preventDefault();
        novaPesquisa();
    });

    $('#btnSalvarComparada').on('click', function (e) {
        e.preventDefault();
        let prevencaoEmAnalise = obterPrevencaoEmAnalise();

        if (prevencaoEmAnalise && prevencaoEmAnalise.requisicaoAnterior) {
            let observacaoEspelho = window.listaExportacaoObservacao.get(prevencaoEmAnalise.requisicaoAnterior);

            createModal.open();
            setTimeout(() => {
                let modal = document.querySelector(".modal"); // Ajuste para a classe/campo do modal
                let inputElement = modal?.querySelector(".custom-textarea"); // Busca dentro do modal

                if (inputElement) {
                    inputElement.value = observacaoEspelho ?? '';
                    inputElement.dispatchEvent(new Event("input")); // Se for um componente reativo
                }
            }, 300);
        }
    });

    $('#btnOcultarSecaoTopo').on('click', function () {
        $.fn.dataTable.tables({ "filter": '.lista-dados', "api": true }).columns.adjust();
    });

    $(document).on('cadastroJustificativa:updated', function () {
        abp.notify.success('Salvo com sucesso!');
        novaPesquisa();
    });

    // Intercepta todas as requisições AJAX do DataTables
    $(document).ajaxError(function (event, jqXHR) {
        // Garante que a UI será desbloqueada
        abp.ui.unblock();

        resetRequisicoesDataTable();
    });

    function inicializarRequisicoesDataTable() {
        return $('#PrevencaoRequisicoesTable')
            // Bloqueia a tela antes de enviar a requisição ajax.
            .on('preXhr.dt', function () { abp.ui.block({ elm: 'body', busy: false }); })
            // Intercepta o evento após a requisição (sucesso ou erro) --> só está disparando após sucesso. Talvez o abp.libs.datatables.createAjax() esteja "tratando" o erro?
            .on('xhr.dt', function () { abp.ui.unblock(); })
            // Desbloqueia a tela em caso de erro (ex: timeout).
            .on('error.dt', function () { abp.ui.unblock(); })
            // Configura o datatable.
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                processing: true,
                serverSide: true,
                deferLoading: 0, // Delay the loading of server-side data until second draw.
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'numeroProtocoloRequisicao',
                ajax: abp.libs.datatables.createAjax(service.getRequisicoes, obterFiltrosPesquisa),
                columnDefs: [
                    {
                        title: "Requisição",
                        data: "numeroProtocoloRequisicao"
                    }
                ],
                drawCallback: function (settings) {
                    let tabela = this.api();
                    // Seleciona a primeira linha.
                    selecionarLinhaTabela(tabela, 0);
                }
            }))
            .on('select', function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarRequisicaoEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="PrevencaoRequisicoesTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="PrevencaoRequisicoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function inicializarPrevencoesDataTable() {
        return $('#PrevencoesTable')
            .DataTable(abp.libs.datatables.normalizeConfiguration({
                paging: false,
                searching: false, // Disable default searchbox.
                autoWidth: false,
                scrollY: 100,
                ordering: false,
                select: { style: 'single', info: false, toggleable: false },
                rowId: 'requisicaoAnterior',
                columnDefs: [
                    { title: "Requisição Anterior", data: "requisicaoAnterior" },
                    { title: "Originário", data: "numeroProcessoOriginario" },
                    { title: "Detalhe", data: "descricao" }
                ]
            }))
            .on('select', async function (e, dt, type, indexes) {
                if (type === 'row') {
                    atualizarPrevencaoEmAnalise();

                    // Verifica se deve desabilitar setas de navegação (se está no primeiro/último item).
                    let linhaSelecionada = indexes[0];
                    let totalLinhas = dt.rows().count();

                    $('[data-tipo-navegacao="anterior"][data-tabela="PrevencoesTable"]').prop('disabled', linhaSelecionada == 0);
                    $('[data-tipo-navegacao="proximo"][data-tabela="PrevencoesTable"]').prop('disabled', linhaSelecionada + 1 >= totalLinhas);
                }
            });
    }

    function obterFiltrosPesquisa() {
        const input = {};
        $('#AnalisePrevencaoFilterInput')
            .serializeArray()
            .forEach(function (data) {
                if (data.value != '') {
                    input[abp.utils.toCamelCase(data.name.replace(/AnalisePrevencaoFilterInput./g, ''))] = data.value;
                }
            })
        return input;
    };

    function obterRequisicaoEmAnalise() { return requisicoesDataTable.row({ selected: true }).id() };

    function obterPrevencaoEmAnalise() { return prevencoesDataTable.row({ selected: true }).data(); };

    function novaPesquisa() {
        limparRequisicaoEmAnalise();

        // Recarrega a tabela de requisições.
        requisicoesDataTable.ajax.reload();
    }

    function atualizarRequisicaoEmAnalise() {
        limparRequisicaoEmAnalise();

        let requisicaoEmAnalise = obterRequisicaoEmAnalise();

        if (requisicaoEmAnalise) {
            $('#ViewModel_NumeroDaRequisicao').val(requisicaoEmAnalise);

            $('.barra-navegacao button').prop('disabled', false);

            carregarDadosRequisicao(requisicaoEmAnalise, true, false);

            // Recarrega a tabela prevenções.
            abp.ui.block({ elm: 'body', busy: true })

            service.getPrevencoes(requisicaoEmAnalise)
                .then(prevencoes => {
                    if (prevencoes.totalCount == 0) {
                        abp.notify.error('Falha para obter as prevenções.');
                        limparPrevencaoEmAnalise();
                    }
                    else {
                        prevencoesDataTable.clear().rows.add(prevencoes.items).draw();
                        // Seleciona a primeira linha.
                        selecionarLinhaTabela(prevencoesDataTable, 0);
                    }
                })
                .catch(() => {
                    abp.notify.error('Falha para obter as prevenções.');
                    limparPrevencaoEmAnalise();
                })
                .always(() => {
                    abp.ui.unblock();
                });
        }
    }

    function atualizarPrevencaoEmAnalise() {
        let prevencaoEmAnalise = obterPrevencaoEmAnalise();

        if (prevencaoEmAnalise) {
            $('#ViewModel_Prevencao').val(prevencaoEmAnalise.requisicaoAnterior + ' - ' + prevencaoEmAnalise.numeroProcessoOriginario + ' - ' + prevencaoEmAnalise.codTipoPrevencao);

            carregarDadosRequisicao(prevencaoEmAnalise.requisicaoAnterior, false, false);
        }
        else {
            limparPrevencaoEmAnalise();
        }
    }

    function limparRequisicaoEmAnalise() {
        $('#ViewModel_NumeroDaRequisicao').val('');
        $('.barra-navegacao button').prop('disabled', true);

        window.listaExportacaoObservacao = new Map();
        limparDadosRequisicao(true);
        limparPrevencaoEmAnalise();
    }

    function limparPrevencaoEmAnalise() {
        $('#ViewModel_Prevencao').val('');
        $('.botao-navegacao-tabela[data-tabela="PrevencoesTable"]').prop('disabled', true);

        prevencoesDataTable.clear().draw();
        limparDadosRequisicao(false);
    }

    function resetRequisicoesDataTable() {
        requisicoesDataTable.destroy();
        $('#PrevencaoRequisicoesTable').empty();

        limparRequisicaoEmAnalise();
        requisicoesDataTable = inicializarRequisicoesDataTable();
    }
});
