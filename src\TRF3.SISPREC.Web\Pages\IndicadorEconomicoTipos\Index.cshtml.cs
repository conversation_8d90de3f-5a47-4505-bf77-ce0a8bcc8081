using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Form;

namespace TRF3.SISPREC.Web.Pages.TipoIndicadorEconomicos;

public class IndexModel : SISPRECPageModel
{
    public IndicadorEconomicoTipoFilterInput IndicadorEconomicoTipoFilter { get; set; } = new();

    public virtual async Task OnGetAsync()
    {
        await Task.CompletedTask;
    }
}

public class IndicadorEconomicoTipoFilterInput
{
    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Id Tipo")]
    [Range(0, int.MaxValue, ErrorMessage = "O ID deve estar entre {1} e {2}.")]
    public int? TipoIndicadorEconomicoId { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Código Tipo")]
    [MaxLength(3, ErrorMessage = "O código deve ter no máximo {1} caracteres")]
    public string? Codigo { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Descrição Tipo")]
    [MaxLength(50, ErrorMessage = "A descrição deve ter no máximo {1} caracteres")]
    public string? Descricao { get; set; }

    [FormControlSize(AbpFormControlSize.Small)]
    [Display(Name = "Ativo")]
    public ESimNao? Ativo { get; set; }

}
