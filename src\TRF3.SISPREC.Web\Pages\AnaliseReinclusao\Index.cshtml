@page
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model TRF3.SISPREC.Web.Pages.AnaliseReinclusao.IndexModel
@{
    PageLayout.Content.Title = "Análise de Reinclusão";

    ViewData["Meses"] = Model.MesesComItemVazio;
}

@section scripts
{
    <abp-script src="/js/util.js" />
    <abp-script src="/js/componente-utils.js" />
    <abp-script src="/js/analises-utils.js" />
    <abp-script src="/Pages/AnaliseReinclusao/index.js" />
}

@section styles
{
    <abp-style src="/css/app/analises.css" />
    <abp-style src="/Pages/AnaliseReinclusao/index.css" />
}

<abp-card>
    <abp-card-body>

        <!--BOTÃO RECOLHER/EXPANDIR-->
        <div class="tooltip-container float-end" style="margin:-1.5rem">
            <abp-button class="ocultar-secao-botao" id="btnOcultarSecaoTopo" size="Small" button-type="Default" abp-collapse-id="ocultarSecaoTopo">ᐱ</abp-button>
            <span class="tooltiptext" id="tooltipText">Recolher</span>
        </div>

        <!--SEÇÃO OCULTAR E EXIBIR-->
        <abp-collapse-body id="ocultarSecaoTopo" show="true">
            <form asp-for="AnaliseReinclusaoFilterInput" id="AnaliseReinclusaoFilterInput">
                <partial name="Shared/Partials/_AnaliseFiltroForm" model="@Model.AnaliseReinclusaoFilterInput" />
            </form>

            <!--LISTAS-->
            <abp-row class="lista-dados">
                <abp-column style="max-width: 100%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="ReinclusaoRequisicoesTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
            </abp-row>
        </abp-collapse-body>

        <!--BOTÕES-->
        <abp-row class="d-flex button-container barra-navegacao">
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="ReinclusaoRequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="width: 135px; margin: -6px;">
                <abp-input asp-for="ViewModel!.NumeroDaRequisicao" readonly="true" />
            </abp-column>
            <abp-column class="col-auto" style="width: 135px; margin-right: -6px;">
                <abp-input asp-for="ViewModel!.NumeroDaRequisicaoOriginal" readonly="true" />
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="ReinclusaoRequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Salvar Requisição Comparada" id="btnSalvarComparada"></abp-button>
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Cadastrar Justificativa" id="btnCadastrarJustificativa" data-tipo-analise="4"></abp-button>
            </abp-column>
        </abp-row>

        <abp-column style="width: 100%;">
            <abp-table small="true" border-style="Bordered" id="EstornoTable" class="nowrap" style="line-height: 5px" />
        </abp-column>

        @await Html.PartialAsync("Components/Analises/_SecaoComparacaoView")

    </abp-card-body>
</abp-card>
