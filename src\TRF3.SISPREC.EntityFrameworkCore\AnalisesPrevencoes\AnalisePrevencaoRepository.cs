using Microsoft.EntityFrameworkCore;
using TRF3.SISPREC.AnalisePrevencoes;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.OcorrenciaMotivos;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using TRF3.SISPREC.RequisicoesVerificacoes;
using TRF3.SISPREC.VerificacaoTipos;
using Volo.Abp.EntityFrameworkCore;

namespace TRF3.SISPREC.AnalisesPrevencoes;

public class AnalisePrevencaoRepository : IAnalisePrevencaoRepository
{
    private readonly IDbContextProvider<SISPRECDbContext> _dbContextProvider;

    public AnalisePrevencaoRepository(IDbContextProvider<SISPRECDbContext> dbContextProvider)
    {
        _dbContextProvider = dbContextProvider;
    }

    public async Task<List<RequisicaoPrevencoes>> ObterRequisicoes(
        ETipoProcedimentoRequisicao? tipoProcedimento,
        int? ano,
        int? mes,
        DateTime? dataInicio,
        DateTime? dataTermino,
        string? numeroRequisicao)
    {
        var context = await _dbContextProvider.GetDbContextAsync();

        var queryPrevencao =
            from rpc in context.Set<RequisicaoProtocolo>().AsNoTracking()
            join rrp in context.Set<RequisicaoProposta>().AsNoTracking()
                on rpc.NumeroProtocoloRequisicao equals rrp.NumeroProtocoloRequisicao
            join pro in context.Set<Proposta>().AsNoTracking()
                on rrp.PropostaId equals pro.PropostaId
            join oco in context.Set<RequisicaoOcorrencia>().AsNoTracking()
                on rpc.NumeroProtocoloRequisicao equals oco.NumeroProtocoloRequisicaoId
            join ocm in context.Set<OcorrenciaMotivo>().AsNoTracking()
                on oco.CodigoMotivoOcorrenciaId equals ocm.OcorrenciaMotivoId
            where ocm.AnaliseTelaId == (int)EDescricaoAnaliseTela.PREVENCAO
            join rj in context.Set<RequisicaoJustificativa>().AsNoTracking()
                on new { NumProtocolo = rpc.NumeroProtocoloRequisicao, AnaliseTela = (int)EDescricaoAnaliseTela.PREVENCAO }
                equals new { NumProtocolo = rj.NumeroProtocoloRequisicao, AnaliseTela = rj.AnaliseTelaId }
                into rjGroup
            from rj in rjGroup.DefaultIfEmpty()
            where rj.NumeroProtocoloRequisicao == null && rpc.SituacaoRequisicaoId == (int)ESituacaoRequisicao.PENDENTE // Verifica se não há justificativa e está pendente
            && !(
                from rv in context.Set<RequisicaoVerificacao>().AsNoTracking()
                join vt in context.Set<VerificacaoTipo>().AsNoTracking()
                    on rv.VerificacaoTipoId equals vt.VerificacaoTipoId
                where
                rv.NumeroProtocoloRequisicaoId == rpc.NumeroProtocoloRequisicao
                && vt.AnaliseTelaId == (int)EDescricaoAnaliseTela.PREVENCAO
                && !rv.Executado
                select rv
            ).Any()
            select new { rpc, pro };

        if (!string.IsNullOrEmpty(numeroRequisicao))
            queryPrevencao = queryPrevencao.Where(req => req.rpc.NumeroProtocoloRequisicao == numeroRequisicao);
        else
        {
            queryPrevencao = queryPrevencao
                .Where(req => req.pro.TipoProcedimentoId == tipoProcedimento.ToString() && req.pro.AnoProposta == ano && req.pro.MesProposta == mes)
                .WhereIf(dataInicio.HasValue, req => req.rpc.DataHoraProtocoloRequisicao >= dataInicio)
                .WhereIf(dataTermino.HasValue, req => req.rpc.DataHoraProtocoloRequisicao <= dataTermino!.Value.Date.AddDays(1).AddTicks(-1));
        }

        return await queryPrevencao
            .Select(q => new RequisicaoPrevencoes { NumeroProtocoloRequisicao = q.rpc.NumeroProtocoloRequisicao })
            .Distinct()
            .OrderBy(x => x.NumeroProtocoloRequisicao)
            .ToListAsync();
    }

    public async Task<List<Prevencao>> ObterPrevencoes(string numeroRequisicao)
    {
        var context = await _dbContextProvider.GetDbContextAsync();

        var query =
            from rpc in context.Set<RequisicaoProtocolo>().AsNoTracking()
            join rrp in context.Set<RequisicaoProposta>().AsNoTracking() on rpc.NumeroProtocoloRequisicao equals rrp.NumeroProtocoloRequisicao
            join pro in context.Set<Proposta>().AsNoTracking() on rrp.PropostaId equals pro.PropostaId
            join oco in context.Set<RequisicaoOcorrencia>().AsNoTracking() on rpc.NumeroProtocoloRequisicao equals oco.NumeroProtocoloRequisicaoId
            join ocm in context.Set<OcorrenciaMotivo>().AsNoTracking() on oco.CodigoMotivoOcorrenciaId equals ocm.OcorrenciaMotivoId
            where (ocm.AnaliseTelaId == (int)EDescricaoAnaliseTela.PREVENCAO)
            join rj in context.Set<RequisicaoJustificativa>().AsNoTracking() on rpc.NumeroProtocoloRequisicao equals rj.NumeroProtocoloRequisicao into rjGroup
            from rj in rjGroup.DefaultIfEmpty().Where(x => x.AnaliseTelaId == (int)EDescricaoAnaliseTela.PREVENCAO)
            where rpc.NumeroProtocoloRequisicao == numeroRequisicao &&
                  rpc.SituacaoRequisicaoId == (int)ESituacaoRequisicao.PENDENTE &&
                  rj.NumeroProtocoloRequisicao == null
            select new Prevencao
            {
                CodOcorrencia = ocm.CodigoMotivo,
                RequisicaoAnterior = oco.NumeroProtocoloRequisicaoAnterior ?? string.Empty,
                NumeroProcessoOriginario = oco.NumeroProcessoOrigem ?? string.Empty,
                Descricao = ocm.DescricaoMotivo,
                CodTipoPrevencao = OcorrenciaMotivoConsts.ObterTipoPrevencaoPorCodigoMotivo(ocm.CodigoMotivo)
            };

        return await query
            .OrderBy(x => x.RequisicaoAnterior)
            .ThenBy(x => x.NumeroProcessoOriginario)
            .ThenBy(x => x.Descricao)
            .ToListAsync();
    }
}