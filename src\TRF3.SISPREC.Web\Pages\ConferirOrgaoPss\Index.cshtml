@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using TRF3.SISPREC.Web.Pages.ConferirOrgaoPss
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Análise de Órgão PSS";
    PageLayout.Content.MenuItemName = SISPRECMenus.ConferirOrgaoPss;
}

@section scripts
{
    <abp-script src="/js/exportacao-pdf.js" />
    <abp-script src="/Pages/ConferirOrgaoPss/index.js" />
    <abp-script src="/js/exportacao-pdf.js" />
    <abp-script src="/js/componente-utils.js" />
}
@section styles
{
    <abp-style src="/Pages/ConferirOrgaoPss/index.css" />
}

<abp-card>
    <abp-card-body>
        <abp-dynamic-form abp-model="ConsultaFilter" id="ConsultaFilter" required-symbols="false" column-size="_3">
            <abp-form-content />
        </abp-dynamic-form>
        <abp-column class="d-flex align-items-start">
            <abp-button size="Small" class="mx-0" button-type="Primary" id="btnPesquisar">Pesquisar</abp-button>
            <abp-button block="true" size="Small" id="btnExportarPDF" class="btn-primary-outline custom-border mx-1">
                Exportar PDF
            </abp-button>
        </abp-column>
        <hr />
        <abp-table striped-rows="true" id="ConferirOrgaoPssTable" class="nowrap" />
        <abp-row class="mt-3">
            <abp-column class="text-start">
                <abp-button id="marcar-todos" text="Marcar/Desmarcar Todos" button-type="Outline_Primary" size="Medium" />
            </abp-column>
            <abp-column class="text-end">
                <abp-button id="btnCadastrarJustificativa" text="Cadastrar Justificativa" data-tipo-analise="6" button-type="Outline_Primary" size="Medium" />
            </abp-column>
        </abp-row>
    </abp-card-body>
</abp-card>
