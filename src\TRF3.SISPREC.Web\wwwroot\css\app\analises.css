body {
    overflow-x: hidden;
}

.tooltip-container {
    position: relative;
    display: inline-block;
}

    .tooltip-container .tooltiptext {
        visibility: hidden;
        width: 120px;
        background-color: black;
        color: #fff;
        text-align: center;
        border-radius: 5px;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 95%;
        margin-left: -120px;
        opacity: 0;
        transition: opacity 0.3s;
    }

    .tooltip-container:hover .tooltiptext {
        visibility: visible;
        opacity: 1;
    }

.ocultar-secao-botao {
    margin-bottom: 0px;
    width: 40px;
    height: 25px;
    padding: 0px;
    font-size: 10px;
    z-index: 1000;
    top: 70px;
    right: 30px;
}

.button-container {
    gap: 8px;
    align-items: center;
}

    .button-container div:not(:first-child) {
        padding: 0;
    }

    .button-container div {
        padding-right: 0;
    }

.lista-dados {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 1rem;
}

    .lista-dados .dt-container .dt-scroll div.dt-scroll-body {
        min-height: 100px;
    }

    .lista-dados table > tbody > tr:hover {
        cursor: pointer;
    }

    .lista-dados table > tbody > tr.selected > *, .lista-dados table.table-striped > tbody > tr.selected > * {
        color: #fff !important;
    }

.barra-navegacao label {
    font-size: smaller !important;
}

.custom-textarea {
    height: auto !important;
}

.inputVerde {
    font-weight: 700;
    background-color: #00ff68 !important;
    color: #062a44 !important;
}

.inputVermelho {
    font-weight: 700;
    background-color: #fb4545 !important;
    color: #062a44 !important;
}

#secaoComparacao {
    white-space: nowrap;
    overflow-x: scroll;
}

    #secaoComparacao table {
        margin-bottom: 8px;
    }

        #secaoComparacao table th {
            font-weight: bolder;
            color: #222;
        }

        #secaoComparacao table tr {
            height: 24px;
            line-height: 0;
            vertical-align: middle;
        }

        #secaoComparacao table select {
            width: 100%;
            color: black;
            border: none;
            height: fit-content;
        }