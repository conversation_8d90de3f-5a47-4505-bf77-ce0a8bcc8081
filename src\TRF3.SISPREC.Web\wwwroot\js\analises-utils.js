function carregarDadosRequisicao(numeroProtocoloRequisicao, ehPrincipal, estilizarPersonalizado) {

    const serviceAnalise = tRF3.sISPREC.analises.analises;

    const classeLinha = ehPrincipal ? 'requisicao-principal' : 'requisicao-comparada';

    limparDadosRequisicao(ehPrincipal);
    serviceAnalise.compararRequisicoes(numeroProtocoloRequisicao)
        .then(data => {
            if (data && data.length > 0) {
                preencherLinhas(data, ehPrincipal, classeLinha)
            }
            else {
                abp.notify.error(`Falha para obter dados da requisição ${numeroProtocoloRequisicao}.`);
                return null;
            }
        })
        .then(() => {
            if (!estilizarPersonalizado) {
                executarComparacao('[name^="td"], select[name^="combo"]', 'destaque-amarelo');
            }
            else {
                executarComparacaoPersonalizada();
            }
        })
        .catch(() => {
            abp.notify.error(`Falha para obter dados da requisição ${numeroProtocoloRequisicao}.`);
            return null;
        });

    carregarComboComDados(serviceAnalise, 'buscaOriginariosAnalise', 'comboOriginario', classeLinha, numeroProtocoloRequisicao);
    carregarComboComDados(serviceAnalise, 'buscaExpedientesAnalise', 'comboExpediente', classeLinha, numeroProtocoloRequisicao);
    carregarComboComDados(serviceAnalise, 'buscaContratuaisAnalise', 'comboContratual', classeLinha, numeroProtocoloRequisicao);
    carregarComboComDados(serviceAnalise, 'buscaSemelhantesAnalise', 'comboSemelhante', classeLinha, numeroProtocoloRequisicao);
    carregarComboComDados(serviceAnalise, 'buscaReferenciasAnalise', 'comboReferencia', classeLinha, numeroProtocoloRequisicao);
}

function preencherLinhas(dados, ehPrincipal, classeLinha) {
    const mapeamentoCampos = {
        "tdRequisicao": "numeroProtocoloRequisicao",
        "tdProcedimento": "tipoProcedimento",
        "tdProposta": "anoProposta",
        "tdMes": "mesProposta",
        "tdTipoRequisicao": "indicadorInclusaoRequisicao",
        "tdNatureza": "naturezaCredito",
        "tdHonorario": "tipoHonorario",
        "tdTipo": "tipoRequisicao",
        "tdSituacaoProposta": "situacaoRequisicaoProposta",
        "tdSituacaoRequisicao": "descricaoSituacao",
        "tdValor": "valorRequisicao",
        "tdDataConta": "dataContaLiquidacao",
        "tdCpfCnpjRequerido": "numeroCnpjCpfPessoaRequerido",
        "tdRequerido": "nomePessoaRequerido",
        "tdCpfCnpjAutor": "numeroCnpjCpfPessoaAutor",
        "tdParteAutora": "nomePessoaAutor",
        "tdCpfCnpjRequerente": "numeroCnpjCpfPessoaRequerente",
        "tdRequerente": "nomePessoaRequerente",
        "tdAdvogadoCpfCnpj": "numeroCnpjCpfAdvogado",
        "tdAdvogado": "nomeAdvogado",
        "tdAssunto": "descricaoCJFAssunto",
        "tdDataProtocolo": "dataHoraProtocoloRequisicao",
        "tdOficioJuizo": "numeroOficioRequisitorio",
        "tdTransitoConhecimento": "dataTransitoJulgadoFase",
        "tdTransitoEmbargos": "dataTransitoJulgadoEmbargos",
        "tdCodigoTipoIndicadorEconomico": "codigoTipoIndicadorEconomico",
        "tdObservacao": "observacao",
        "tdIndicadorEstorno": "indicadorEstornoRequisicao",
        "tdAtualizacaoAtual": "valorAtualizadoRequisicaoAtual",
        "tdAtualizacaoAnterior": "valorAtualizadoRequisicaoAnterior"
    };

    dados.forEach(item => {
        Object.entries(mapeamentoCampos).forEach(([name, campo]) => {
            // Valor atualizado atual é preenchido apenas para requisição Principal.
            // Valor atualizado anterior é preenchido apenas para requisição Comparada.
            if ((ehPrincipal && name !== 'tdAtualizacaoAnterior')
                || (!ehPrincipal && name != 'tdAtualizacaoAtual')) {
                $(`.${classeLinha} [name="${name}"]`).text(item[campo] || '');
            }
        });
    });

    calcularSomaValoresAtualizados();

    // Implementar regra de preenchimento quando a tabela tab_estorno_lei_13463 for migrada do TRF_UFEP.
    let teveEstorno = false;
    $('#ckEstornoLei').prop('checked', teveEstorno);
    $('#ckRecomposta').prop('checked', teveEstorno && dados.some(d => d.indicadorEstornoRequisicao === 'ATIVA'));
}

function carregarComboComDados(service, metodo, selectName, classe, numeroProtocoloRequisicao) {
    const obterDados = (numeroProtocoloRequisicao) => {
        return service[metodo](numeroProtocoloRequisicao)
            .then(data => (Array.isArray(data) && data.length > 0 ? data : null))
            .catch(error => {
                console.error(`Erro ao buscar dados com o método ${metodo}: `, error);
                return null;
            });
    };

    const preencherCombo = (dados) => {
        const select = $(`.${classe} [name="${selectName}"]`);
        select.empty();

        if (dados) {
            dados.forEach((item, index) => {
                const option = `<option value="${index}">${item}</option>`;
                select.append(option);
            });
        }
    };

    obterDados(numeroProtocoloRequisicao).then(data => preencherCombo(data));
}

function limparDadosRequisicao(ehPrincipal) {
    if (ehPrincipal) {
        limparDadosTabelas('requisicao-principal');
        limparDadosTabelas('requisicao-comparada');
    }
    else {
        limparDadosTabelas('requisicao-comparada');
    }
}

function limparDadosTabelas(classe) {
    $(`#secaoComparacao .${classe}`).each(function () {
        $(this).find('td:not(:has(select))').text('').css('background-color', '');
        $(this).find('select').empty();
        $(this).find('input[type="checkbox"]').prop('checked', false);
    });
    $('#secaoComparacao .destaque-amarelo').removeClass('destaque-amarelo');
    $('#secaoComparacao .destaque-verde').removeClass('destaque-verde');
    $('#secaoComparacao .destaque-laranja').removeClass('destaque-laranja');
    $('#secaoComparacao .destaque-vermelho').removeClass('destaque-vermelho');
}

function calcularSomaValoresAtualizados() {
    const valorAtual = $('#tdAtualizacaoAtual').text();
    const valorAnterior = $('#tdAtualizacaoAnterior').text();

    const valorAtualLimpo = valorAtual.replace(/\./g, '').replace(',', '.');
    const valorAnteriorLimpo = valorAnterior.replace(/\./g, '').replace(',', '.');

    const valorAtualConvertido = parseFloat(valorAtualLimpo) || 0;
    const valorAnteriorConvertido = parseFloat(valorAnteriorLimpo) || 0;

    const soma = valorAtualConvertido + valorAnteriorConvertido;

    $('#tdSomaDosValoresAtualizados').text(formatarValorMonetario(soma));
    $('#tdAtualizacaoAtual').text(valorAtual || '0,00');
    $('#tdAtualizacaoAnterior').text(valorAnterior || '0,00');
}
