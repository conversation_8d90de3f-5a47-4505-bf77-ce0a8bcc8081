using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using TRF3.SISPREC.Enums;
using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.OcorrenciaMotivos;

namespace TRF3.SISPREC.Web.Pages.OcorrenciaMotivos.ViewModels;

[ExcludeFromCodeCoverage]
public class EditOcorrenciaMotivoViewModel
{
    [HiddenInput]
    public int OcorrenciaMotivoId { get; set; }

    [HiddenInput]
    [Display(Name = "Código do Motivo")]
    [Range(0, OcorrenciaMotivoConsts.CODIGO_MOTIVO_TAMANHO_MAX, ErrorMessage = "O valor do Código do Motivo deve estar entre {1} e {2}.")]
    public int CodigoMotivo { get; set; }

    [Display(Name = "Ação Tipo")]
    public EDescricaoAcaoTipo AcaoTipoId { get; set; }
    public List<SelectListItem> AcaoTipoLookupList { get; set; } = new();

    [Display(Name = "Tipo Análise")]
    public int? AnaliseTelaId { get; set; }
    public List<SelectListItem> AnaliseTelaLookupList { get; set; } = new();

    [Display(Name = "Descrição Motivo")]
    [Required]
    public string? DescricaoMotivo { get; set; }

    [Display(Name = "Ativo")]
    public bool Ativo { get; set; } = true;
}
