using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicaoJustificativas;
using static TRF3.SISPREC.EntityFrameworkCore.Consts.Constants;

namespace TRF3.SISPREC.EntityFrameworkCore.Configuration
{
    internal sealed class RequisicaoJustificativaConfiguration : IEntityTypeConfiguration<RequisicaoJustificativa>
    {
        private const string NOME_TABELA = $"{PrefixTable.ANA}_REQUISICAO_JUSTIFICATIVA";

        public void Configure(EntityTypeBuilder<RequisicaoJustificativa> builder)
        {
            builder
                .ToTable($"{NOME_TABELA}");

            builder
                .HasKey(x => x.RequisicaoJustificativaId)
                .HasName($"{NOME_TABELA}_P01");

            builder
                .Property(x => x.RequisicaoJustificativaId)
                .HasColumnName("SEQ_REQUIS_JUSTIF")
                .IsRequired();

            builder
                .Property(x => x.NumeroProtocoloRequisicao)
                .HasColumnName("NUM_PROTOC_REQUIS")
                .HasColumnType("CHAR(11)")
                .IsRequired();

            builder
                .Property(x => x.AnaliseTelaId)
                .HasColumnName("SEQ_ANALIS_TELA")
                .IsRequired();

            builder
                .Property(x => x.AcaoJustificativaId)
                .HasColumnName("SEQ_ACAO_JUSTIF")
                .IsRequired();

            builder
                .Property(x => x.ComplementoMotivo)
                .HasColumnName("DES_JUSTIF_COMPLE")
                .HasMaxLength(RequisicaoJustificativaConsts.DES_JUSTIF_COMPLE_TAMANHO_MAX)
                .HasColumnType("varchar");

            builder
                .Property(x => x.DataAnalise)
                .HasColumnName("DAT_ANALIS")
                .IsRequired();

            builder
                .Property(x => x.NomeUsuario)
                .HasColumnType("VARCHAR(50)")
                .HasColumnName("NOM_USUARI")
                .IsRequired();

            builder
                .Property(x => x.IsDeleted)
                .HasColumnName("SIN_EXCLUI")
                .HasDefaultValue(false)
                .IsRequired();

            builder
                .Property(x => x.Observacoes)
                .HasColumnName("DES_OBSERV");

            builder
                .HasOne(x => x.RequisicaoProtocolo)
                    .WithMany(x => x.RequisicaoJustificativas)
                        .HasForeignKey(x => x.NumeroProtocoloRequisicao)
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName($"{NOME_TABELA}_R01");

            builder
                .HasOne(x => x.AnaliseTela)
                    .WithMany(x => x.RequisicaoJustificativa)
                        .HasForeignKey(x => x.AnaliseTelaId)
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName($"{NOME_TABELA}_R02");

            builder
                .HasOne(x => x.AcaoJustificativa)
                    .WithMany(x => x.RequisicaoJustificativa)
                        .HasForeignKey(x => x.AcaoJustificativaId)
                        .OnDelete(DeleteBehavior.NoAction)
                        .HasConstraintName($"{NOME_TABELA}_R03");
        }
    }
}
