using TRF3.SISPREC.Enums;
using Volo.Abp.DependencyInjection;

namespace TRF3.SISPREC.AnalisePrevencoes;

public interface IAnalisePrevencaoRepository : ITransientDependency
{
    Task<List<RequisicaoPrevencoes>> ObterRequisicoes(
        ETipoProcedimentoRequisicao? tipoProcedimento,
        int? ano,
        int? mes,
        DateTime? dataInicio,
        DateTime? dataTermino,
        string? numeroRequisicao);

    Task<List<Prevencao>> ObterPrevencoes(string numeroRequisicao);
}
