@page
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model dynamic

@{
    Layout = null;
}

<div id="secaoComparacao">
    <!--REQUISICAO-->
    <abp-table small="true" striped-rows="true" border-style="Bordered">
        <thead>
            <tr>
                <th scope="Column">Requisição</th>
                <th scope="Column">Procedimento</th>
                <th scope="Column">Proposta</th>
                <th scope="Column">Mês</th>
                <th scope="Column">Tipo Requisição</th>
                <th scope="Column">Natureza</th>
                <th scope="Column">Honorário</th>
                <th scope="Column">Tipo (T/C/S/I)</th>
                <th scope="Column">Situação Proposta</th>
                <th scope="Column">Situação Requisição</th>
                <th scope="Column">Valor</th>
                <th scope="Column">Data Conta</th>
            </tr>
        </thead>
        <tbody>
            <tr class="requisicao-principal">
                <td name="tdRequisicao"></td>
                <td name="tdProcedimento"></td>
                <td name="tdProposta"></td>
                <td name="tdMes"></td>
                <td name="tdTipoRequisicao"></td>
                <td name="tdNatureza"></td>
                <td name="tdHonorario"></td>
                <td name="tdTipo"></td>
                <td name="tdSituacaoProposta"></td>
                <td name="tdSituacaoRequisicao"></td>
                <td name="tdValor"></td>
                <td name="tdDataConta"></td>
            </tr>
            <tr class="requisicao-comparada">
                <td name="tdRequisicao"></td>
                <td name="tdProcedimento"></td>
                <td name="tdProposta"></td>
                <td name="tdMes"></td>
                <td name="tdTipoRequisicao"></td>
                <td name="tdNatureza"></td>
                <td name="tdHonorario"></td>
                <td name="tdTipo"></td>
                <td name="tdSituacaoProposta"></td>
                <td name="tdSituacaoRequisicao"></td>
                <td name="tdValor"></td>
                <td name="tdDataConta"></td>
            </tr>
        </tbody>
    </abp-table>

    <!--REQUERIDO-->
    <abp-table small="true" striped-rows="true" border-style="Bordered">
        <thead>
            <tr>
                <th scope="Column" style="width: 11%;">CPF/CNPJ Requerido</th>
                <th scope="Column" style="width: 39%;">Requerido</th>
                <th scope="Column" style="width: 11%;">CPF/CNPJ Parte Autora</th>
                <th scope="Column" style="width: 39%;">Parte Autora</th>
            </tr>
        </thead>
        <tbody>
            <tr class="requisicao-principal">
                <td name="tdCpfCnpjRequerido"></td>
                <td name="tdRequerido"></td>
                <td name="tdCpfCnpjAutor"></td>
                <td name="tdParteAutora"></td>
            </tr>
            <tr class="requisicao-comparada">
                <td name="tdCpfCnpjRequerido"></td>
                <td name="tdRequerido"></td>
                <td name="tdCpfCnpjAutor"></td>
                <td name="tdParteAutora"></td>
            </tr>
        </tbody>
    </abp-table>

    <!--REQUERENTE-->
    <abp-table small="true" striped-rows="true" border-style="Bordered">
        <thead>
            <tr>
                <th scope="Column" style="width: 11%;">CPF/CNPJ Requerente</th>
                <th scope="Column" style="width: 39%;">Requerente</th>
                <th scope="Column" style="width: 11%;">CPF/CNPJ Advogado</th>
                <th scope="Column" style="width: 39%;">Advogado</th>
            </tr>
        </thead>
        <tbody>
            <tr class="requisicao-principal">
                <td name="tdCpfCnpjRequerente"></td>
                <td name="tdRequerente"></td>
                <td name="tdAdvogadoCpfCnpj"></td>
                <td name="tdAdvogado"></td>
            </tr>
            <tr class="requisicao-comparada">
                <td name="tdCpfCnpjRequerente"></td>
                <td name="tdRequerente"></td>
                <td name="tdAdvogadoCpfCnpj"></td>
                <td name="tdAdvogado"></td>
            </tr>
        </tbody>
    </abp-table>

    <!--ASSUNTO-->
    <abp-table small="true" striped-rows="true" border-style="Bordered">
        <thead>
            <tr>
                <th scope="Column" style="width: 60%">Assunto</th>
                <th scope="Column" style="width: 10%">Data Protocolo</th>
                <th scope="Column" style="width: 8%">Ofício do Juízo</th>
                <th scope="Column" style="width: 10%">Trâns. Conhecimento</th>
                <th scope="Column" style="width: 9%">Trâns. Embargos</th>
                <th scope="Column" style="width: 6%">Atualização</th>
            </tr>
        </thead>
        <tbody>
            <tr class="requisicao-principal">
                <td name="tdAssunto"></td>
                <td name="tdDataProtocolo"></td>
                <td name="tdOficioJuizo"></td>
                <td name="tdTransitoConhecimento"></td>
                <td name="tdTransitoEmbargos"></td>
                <td name="tdCodigoTipoIndicadorEconomico"></td>
            </tr>
            <tr class="requisicao-comparada">
                <td name="tdAssunto"></td>
                <td name="tdDataProtocolo"></td>
                <td name="tdOficioJuizo"></td>
                <td name="tdTransitoConhecimento"></td>
                <td name="tdTransitoEmbargos"></td>
                <td name="tdCodigoTipoIndicadorEconomico"></td>
            </tr>
        </tbody>
    </abp-table>

    <!--OBSERVCAO-->
    <div style="display: flex; justify-content: space-between; gap: 20px;">
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 4;">
            <thead>
                <tr>
                    <th scope="Column">Observação</th>
                </tr>
            </thead>
            <tbody>
                <tr class="requisicao-principal"><td name="tdObservacao"></td></tr>
                <tr class="requisicao-comparada"><td name="tdObservacao"></td></tr>
            </tbody>
        </abp-table>

        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
            <div class="requisicao-principal" style="display: flex; align-items: center; margin-top: 0px; margin-left: -15px;">
                <label for="ckEstornoLei">
                    <input type="checkbox" id="ckEstornoLei"> Estorno Lei 13463
                </label>
            </div>
            <div class="requisicao-principal" style="display: flex; align-items: center; margin-bottom: 5px; margin-left: -52px;">
                <label for="ckRecomposta">
                    <input type="checkbox" id="ckRecomposta"> Recomposta
                </label>
            </div>
        </div>

    </div>

    <!--ORIGINARIO-->
    <div style="display: flex; justify-content: space-between; gap: 20px;">
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 2;">
            <thead>
                <tr><th scope="Column">Originário</th></tr>
            </thead>
            <tbody>
                <tr class="requisicao-principal"><td><select name="comboOriginario"></select></td></tr>
                <tr class="requisicao-comparada"><td><select name="comboOriginario"></select></td></tr>
            </tbody>
        </abp-table>

        <!--EXPEDIENTE-->
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 1.3;">
            <thead>
                <tr><th scope="Column">Expediente</th></tr>
            </thead>
            <tbody>
                <tr class="requisicao-principal"><td><select name="comboExpediente"></select></td></tr>
                <tr class="requisicao-comparada"><td><select name="comboExpediente"></select></td></tr>
            </tbody>
        </abp-table>

        <!--SEMELHANTE-->
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 0.7">
            <thead>
                <tr><th scope="Column">Semelhante</th></tr>
            </thead>
            <tbody>
                <tr class="requisicao-principal"><td><select name="comboSemelhante"></select></td></tr>
            </tbody>
        </abp-table>

    </div>

    <div style="display: flex; justify-content: space-between; gap: 20px;">
        <!--CONTRATUAL-->
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 2;">
            <thead>
                <tr><th scope="Column">Contratual</th></tr>
            </thead>
            <tbody>
                <tr class="requisicao-principal"><td><select name="comboContratual"></select></td></tr>
                <tr class="requisicao-comparada"><td><select name="comboContratual"></select></td></tr>
            </tbody>
        </abp-table>

        <!--REFERENCIA-->
        <abp-table small="true" striped-rows="true" border-style="Bordered" style="flex: 1.3;">
            <thead>
                <tr><th scope="Column">Referências</th></tr>
            </thead>
            <tbody>
                <tr class="requisicao-principal"><td><select name="comboReferencia"></select></td></tr>
                <tr class="requisicao-comparada"><td><select name="comboReferencia"></select></td></tr>
            </tbody>
        </abp-table>

        <!--VALORES-->
        <abp-table small="true" striped-rows="false" border-style="Bordered" style="flex: 0.7;">
            <thead>
                <tr><th scope="Column" colspan="2">Soma dos valores atualizados</th></tr>
            </thead>
            <tbody>
                <tr class="requisicao-principal">
                    <th scope="Column" style="background-color: whitesmoke; width: 30%;">Atual</th>
                    <td name="tdAtualizacaoAtual" id="tdAtualizacaoAtual"></td>
                </tr>
                <tr class="requisicao-comparada">
                    <th scope="Column" style="background-color: whitesmoke">Anterior</th>
                    <td name="tdAtualizacaoAnterior" id="tdAtualizacaoAnterior"></td>
                </tr>
                <tr class="requisicao-principal">
                    <th scope="Column" style="background-color: whitesmoke">Soma</th>
                    <td id="tdSomaDosValoresAtualizados"></td>
                </tr>
            </tbody>
        </abp-table>
    </div>
</div>