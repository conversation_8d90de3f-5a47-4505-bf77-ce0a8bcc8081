using Microsoft.EntityFrameworkCore;
using NSubstitute;
using Shouldly;
using TRF3.SISPREC.AnalisePrevencoes;
using TRF3.SISPREC.AnalisesPrevencoes;
using TRF3.SISPREC.EntityFrameworkCore.Tests.TestHelpers;
using TRF3.SISPREC.Enums;
using TRF3.SISPREC.OcorrenciaMotivos;
using TRF3.SISPREC.Propostas;
using TRF3.SISPREC.RequisicaoJustificativas;
using TRF3.SISPREC.RequisicoesOcorrencias;
using TRF3.SISPREC.RequisicoesProcessosOrigens;
using TRF3.SISPREC.RequisicoesPropostas;
using TRF3.SISPREC.RequisicoesProtocolos;
using Volo.Abp.EntityFrameworkCore;

namespace TRF3.SISPREC.EntityFrameworkCore.Repositories;

public class AnalisePrevencaoRepositoryTests : SISPRECEntityFrameworkCoreTestBase
{
    private readonly IDbContextProvider<SISPRECDbContext> _dbContextProvider;
    private readonly IAnalisePrevencaoRepository _repository;
    private readonly SISPRECDbContext _dbContext;

    public AnalisePrevencaoRepositoryTests()
    {
        // Configuração inicial dos mocks
        _dbContext = Substitute.For<SISPRECDbContext>(new DbContextOptions<SISPRECDbContext>());
        _dbContextProvider = Substitute.For<IDbContextProvider<SISPRECDbContext>>();
        _dbContextProvider.GetDbContextAsync().Returns(_dbContext);

        // Inicializa todos os DbSets com listas vazias
        _dbContext.MockDbSet(new List<RequisicaoProtocolo>());
        _dbContext.MockDbSet(new List<Proposta>());
        _dbContext.MockDbSet(new List<RequisicaoProposta>());
        _dbContext.MockDbSet(new List<OcorrenciaMotivo>());
        _dbContext.MockDbSet(new List<RequisicaoOcorrencia>());
        _dbContext.MockDbSet(new List<RequisicaoJustificativa>());

        _repository = new AnalisePrevencaoRepository(_dbContextProvider);
    }

    [Fact]
    public async Task ObterRequisicoes_Deve_Retornar_Requisicao_Pela_Filtro_Requisicao()
    {
        // Arrange
        var numeroProtocolo = "**********";
        ConfigurarMockPadrao(numeroProtocolo);

        // Adiciona ocorrência de CPF/CNPJ sem justificativa
        var ocorrenciasCpfCnpj = new List<OcorrenciaMotivo>
        {
            new()
            {
                OcorrenciaMotivoId = 4,
                AnaliseTelaId = (int)EDescricaoAnaliseTela.CPF_CNPJ
            }
        };

        var requisicoesOcorrenciasCpfCnpj = new List<RequisicaoOcorrencia>
        {
            new()
            {
                NumeroProtocoloRequisicaoId = numeroProtocolo,
                CodigoMotivoOcorrenciaId = 4
            }
        };

        _dbContext.MockDbSet(ocorrenciasCpfCnpj);
        _dbContext.MockDbSet(requisicoesOcorrenciasCpfCnpj);
        _dbContext.MockDbSet(new List<RequisicaoJustificativa>()); // Sem justificativa

        // Act
        var resultado = await _repository.ObterRequisicoes(
            ETipoProcedimentoRequisicao.RPV,
            DateTime.Now.Year,
            DateTime.Now.Month,
            DateTime.Now.AddDays(-1),
            DateTime.Now.AddDays(1),
            numeroProtocolo);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Count.ShouldBe(0);
    }


    [Fact]
    public async Task Nao_Deve_Retornar_Requisicao_Com_Pendencia_CPF_CNPJ_Nao_Justificada()
    {
        // Arrange
        var numeroProtocolo = "**********";
        ConfigurarMockPadrao(numeroProtocolo);

        // Adiciona ocorrência de CPF/CNPJ sem justificativa
        var ocorrenciasCpfCnpj = new List<OcorrenciaMotivo>
        {
            new()
            {
                OcorrenciaMotivoId = 4,
                AnaliseTelaId = (int)EDescricaoAnaliseTela.CPF_CNPJ
            }
        };

        var requisicoesOcorrenciasCpfCnpj = new List<RequisicaoOcorrencia>
        {
            new()
            {
                NumeroProtocoloRequisicaoId = numeroProtocolo,
                CodigoMotivoOcorrenciaId = 4
            }
        };

        _dbContext.MockDbSet(ocorrenciasCpfCnpj);
        _dbContext.MockDbSet(requisicoesOcorrenciasCpfCnpj);
        _dbContext.MockDbSet(new List<RequisicaoJustificativa>()); // Sem justificativa

        // Act
        var resultado = await _repository.ObterRequisicoes(
            ETipoProcedimentoRequisicao.RPV,
            DateTime.Now.Year,
            DateTime.Now.Month,
            DateTime.Now.AddDays(-1),
            DateTime.Now.AddDays(1),
            null);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Count.ShouldBe(0);
    }

    [Fact]
    public async Task ObterPrevencoes_Deve_Passar()
    {
        // Arrange
        var numeroProtocolo = "**********";
        ConfigurarMockPadrao(numeroProtocolo);

        var requisicaoProcessoOrigem = new List<RequisicaoProcessoOrigem>
        {
            new()
            {
                NumeroProtocoloRequisicao = numeroProtocolo
            }
        };

        var requisicaoJustificativa = new List<RequisicaoJustificativa>
        {
            new()
            {
                NumeroProtocoloRequisicao = numeroProtocolo,
                AnaliseTelaId = (int)EDescricaoAnaliseTela.PREVENCAO,
            }
        };

        _dbContext.MockDbSet(requisicaoProcessoOrigem);
        _dbContext.MockDbSet(requisicaoJustificativa);

        // Act
        var resultado = await _repository.ObterPrevencoes(numeroProtocolo);

        // Assert
        resultado.ShouldNotBeNull();
        resultado.Count.ShouldBe(0);
    }

    private void ConfigurarMockPadrao(string numeroProtocolo, List<RequisicaoProtocolo> requisicaoProtocolos = null)
    {
        var dataBase = DateTime.Now;

        // Mock RequisicaoProtocolos
        requisicaoProtocolos ??= new List<RequisicaoProtocolo>
            {
                new()
                {
                    NumeroProtocoloRequisicao = numeroProtocolo,
                    SituacaoRequisicaoId = (int)ESituacaoRequisicao.PENDENTE,
                    DataHoraProtocoloRequisicao = dataBase,
                    StatusProtocoloRequisicao = EStatusProtocoloRequisicao.PROTOCOLADA
                }
            };

        // Mock Propostas
        var propostas = new List<Proposta>
            {
                new()
                {
                    PropostaId = 1,
                    TipoProcedimentoId = ETipoProcedimentoRequisicao.RPV.ToString(),
                    AnoProposta = dataBase.Year,
                    MesProposta = dataBase.Month,
                    SituacaoProposta = ESituacaoProposta.PENDENTE
                }
            };

        // Mock RequisicaoPropostas
        var requisicoesPropostas = new List<RequisicaoProposta>
            {
                new()
                {
                    NumeroProtocoloRequisicao = numeroProtocolo,
                    PropostaId = 1
                }
            };

        // Mock MotivoOcorrencias com AnaliseTelaId null (caso base)
        var motivoOcorrencias = new List<OcorrenciaMotivo>
        {
            new()
            {
                OcorrenciaMotivoId = 1,
                AnaliseTelaId = null // Motivo genérico sem análise específica
            },
            new()
            {
                OcorrenciaMotivoId = 2,
                AnaliseTelaId = (int)EDescricaoAnaliseTela.PREVENCAO
            }
        };

        // Mock RequisicaoOcorrencias com motivo genérico
        var requisicoesOcorrencias = new List<RequisicaoOcorrencia>
        {
            new()
            {
                NumeroProtocoloRequisicaoId = numeroProtocolo,
                CodigoMotivoOcorrenciaId = 1
            }
        };

        // Configura os mocks com dados base
        _dbContext.MockDbSet(requisicaoProtocolos);
        _dbContext.MockDbSet(propostas);
        _dbContext.MockDbSet(requisicoesPropostas);
        _dbContext.MockDbSet(motivoOcorrencias);
        _dbContext.MockDbSet(requisicoesOcorrencias);
        _dbContext.MockDbSet(new List<RequisicaoJustificativa>()); // Inicializa sem justificativas
    }
}
