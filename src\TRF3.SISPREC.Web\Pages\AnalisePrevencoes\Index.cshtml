@page
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@inject IPageLayout PageLayout
@model TRF3.SISPREC.Web.Pages.AnalisePrevencoes.IndexModel
@{
    PageLayout.Content.Title = "Análise de Prevenções";

    ViewData["Meses"] = Model.MesesComItemVazio;
}

@section scripts
{
    <abp-script src="/js/util.js" />
    <abp-script src="/js/componente-utils.js" />
    <abp-script src="/js/analises-utils.js" />
    <abp-script src="/Pages/AnalisePrevencoes/index.js" />
}

@section styles
{
    <abp-style src="/css/app/analises.css" />
}

<abp-card>
    <abp-card-body>

        <!--BOTÃO RECOLHER/EXPANDIR-->
        <div class="tooltip-container float-end" style="margin:-1.5rem">
            <abp-button class="ocultar-secao-botao" id="btnOcultarSecaoTopo" size="Small" button-type="Default" abp-collapse-id="ocultarSecaoTopo">ᐱ</abp-button>
            <span class="tooltiptext" id="tooltipText">Recolher</span>
        </div>

        <!--SEÇÃO OCULTAR E EXIBIR-->
        <abp-collapse-body id="ocultarSecaoTopo" show="true">
            <form asp-for="AnalisePrevencaoFilterInput" id="AnalisePrevencaoFilterInput">
                <partial name="Shared/Partials/_AnaliseFiltroForm" model="@Model.AnalisePrevencaoFilterInput" />
            </form>

            <!--LISTAS-->
            <abp-row class="lista-dados">
                <abp-column style="max-width: 20%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="PrevencaoRequisicoesTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
                <abp-column style="max-width: 80%;">
                    <abp-row>
                        <abp-column>
                            <abp-table responsive="true" striped-rows="true" hoverable-rows="true" id="PrevencoesTable" class="nowrap" />
                        </abp-column>
                    </abp-row>
                </abp-column>
            </abp-row>
        </abp-collapse-body>

        <!--BOTÕES-->
        <abp-row class="d-flex button-container barra-navegacao">
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="PrevencaoRequisicoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="width: 135px; margin: -6px;">
                <abp-input asp-for="ViewModel!.NumeroDaRequisicao" readonly="true" />
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="PrevencaoRequisicoesTable"></abp-button>
            </abp-column>

            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="<" class="botao-navegacao-tabela" data-tipo-navegacao="anterior" data-tabela="PrevencoesTable"></abp-button>
            </abp-column>
            <abp-column class="col-auto" style="width: 350px; margin: -6px;">
                <abp-input asp-for="ViewModel!.Prevencao" readonly="true" />
            </abp-column>
            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text=">" class="botao-navegacao-tabela" data-tipo-navegacao="proximo" data-tabela="PrevencoesTable"></abp-button>
            </abp-column>

            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Salvar Requisição Comparada" id="btnSalvarComparada"></abp-button>
            </abp-column>

            <abp-column class="col-auto">
                <abp-button button-type="Primary" size="Small" disabled="true" text="Cadastrar Justificativa" id="btnCadastrarJustificativa" data-tipo-analise="3"></abp-button>
            </abp-column>
        </abp-row>

        @await Html.PartialAsync("Components/Analises/_SecaoComparacaoView")

    </abp-card-body>
</abp-card>

