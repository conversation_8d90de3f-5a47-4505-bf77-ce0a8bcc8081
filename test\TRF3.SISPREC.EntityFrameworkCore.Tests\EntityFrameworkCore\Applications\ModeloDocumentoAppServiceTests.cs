using Shouldly;
using TRF3.SISPREC.EntityFrameworkCore;
using TRF3.SISPREC.ModelosDocumentos.Dtos;
using TRF3.SISPREC.Setores;
using Volo.Abp.Validation;

namespace TRF3.SISPREC.ModelosDocumentos;

public class ModeloDocumentoAppServiceTests : BaseAppServiceTests<SISPRECEntityFrameworkCoreTestModule>
{
    private readonly IModeloDocumentoAppService _appService;
    private readonly ISetorRepository _setorRepository;
    private readonly IModeloDocumentoRepository _repository;
    private ModeloDocumento? modeloDocumentoObj1;
    private ModeloDocumento? modeloDocumentoObj2;

    public ModeloDocumentoAppServiceTests()
    {
        _appService = GetRequiredService<IModeloDocumentoAppService>();
        _repository = GetRequiredService<IModeloDocumentoRepository>();
        _setorRepository = GetRequiredService<ISetorRepository>();

        WithUnitOfWorkAsync(async () =>
        {

            var setor = new TRF3.SISPREC.Setores.Setor
            {
                Nome = "Teste",
                Sigla = "FIN",
                Ativo = true
            };

            await _setorRepository.InsertAsync(setor, true);

            var fakers = new Bogus.Faker<ModeloDocumento>()
            .RuleFor(p => p.ModeloDocumentoId, p => p.Random.Int(min: 1))
            .RuleFor(p => p.NomeModelo, p => p.Random.Hash(255))
            .RuleFor(p => p.TextoDocumento, p => p.Random.Hash())
            .RuleFor(p => p.SetorId, setor.SetorId)
            .RuleFor(p => p.IsDeleted, false)
            .Generate(2);

            modeloDocumentoObj1 = fakers[0];
            modeloDocumentoObj2 = fakers[1];

            modeloDocumentoObj1 = await _repository.InsertAsync(modeloDocumentoObj1, true);
            modeloDocumentoObj2 = await _repository.InsertAsync(modeloDocumentoObj2, true);
        }).Wait();
    }

    [Fact]
    public async Task Criar_ModeloDocumento_Deve_Passar()
    {
        // Arrange
        var setor = new TRF3.SISPREC.Setores.Setor
        {
            Nome = "Teste",
            Sigla = "FIN",
            Ativo = true
        };

        await _setorRepository.InsertAsync(setor, true);

        var input = new Bogus.Faker<CreateUpdateModeloDocumentoDto>()
            .RuleFor(p => p.NomeModelo, p => p.Random.Hash(255))
            .RuleFor(p => p.TextoDocumento, p => p.Random.Hash())
            .RuleFor(p => p.SetorId, setor.SetorId)
            .RuleFor(p => p.IsDeleted, false)
            .Generate();

        // Act
        var result = await _appService.CreateAsync(input);

        // Assert
        result.ShouldNotBeNull();
        result.ModeloDocumentoId.ShouldBeGreaterThan(0);
        result.NomeModelo.ShouldBe(input.NomeModelo);
        result.TextoDocumento.ShouldBe(input.TextoDocumento);
        result.IsDeleted.ShouldBe(input.IsDeleted);
    }

    [Fact]
    public async Task Criar_ModeloDocumento_Deve_Falhar_Quando_Dados_Invalidos()
    {
        // Arrange
        var input = new CreateUpdateModeloDocumentoDto
        {
            NomeModelo = "teste",
            TextoDocumento = "<p><br></p>",
            SetorId = null
        };

        // Act & Assert
        var exception = await Should.ThrowAsync<AbpValidationException>(() => _appService.CreateAsync(input));

        exception.ValidationErrors.ShouldNotBeNull();
        exception.ValidationErrors.Count.ShouldBe(3);

        var mensagens = exception.ValidationErrors.Select(e => e.ErrorMessage).ToList();
        mensagens.ShouldContain("Nome do modelo e HTML devem estar preenchidos!");
        mensagens.ShouldContain("O campo Texto do Documento é obrigatório e não pode ser vazio.");
        mensagens.ShouldContain("O campo Setor é obrigatório.");

        var membros = exception.ValidationErrors.SelectMany(e => e.MemberNames).ToList();
        membros.ShouldContain(nameof(CreateUpdateModeloDocumentoDto.TextoDocumento));
        membros.ShouldContain(nameof(CreateUpdateModeloDocumentoDto.SetorId));
    }
    [Fact]
    public async Task Atualizar_ModeloDocumento_Deve_Passar()
    {
        // Arrange
        var setor = new TRF3.SISPREC.Setores.Setor
        {
            Nome = "Teste",
            Sigla = "FIN",
            Ativo = true
        };

        await _setorRepository.InsertAsync(setor, true);

        var objetoOriginal = _repository.GetListAsync().Result.FirstOrDefault();
        var input = new Bogus.Faker<CreateUpdateModeloDocumentoDto>()
            .RuleFor(p => p.NomeModelo, p => p.Random.Hash(255))
            .RuleFor(p => p.TextoDocumento, p => p.Random.Hash())
            .RuleFor(p => p.SetorId, setor.SetorId)
            .RuleFor(p => p.IsDeleted, false)
            .Generate();

        // Act
        var result = await _appService.UpdateAsync(objetoOriginal.ModeloDocumentoId, input);

        // Assert
        result.ShouldNotBeNull();
        result.ModeloDocumentoId.ShouldBeGreaterThan(0);
        result.NomeModelo.ShouldBe(input.NomeModelo);
        result.TextoDocumento.ShouldBe(input.TextoDocumento);
        result.IsDeleted.ShouldBe(input.IsDeleted);
    }

    [Fact]
    public async Task Excluir_ModeloDocumento_Deve_Passar()
    {
        // Arrange

        var setor = new TRF3.SISPREC.Setores.Setor
        {
            Nome = "Teste",
            Sigla = "FIN",
            Ativo = true
        };

        await _setorRepository.InsertAsync(setor, true);

        var objetoParaExcluir = new Bogus.Faker<ModeloDocumento>()
            .RuleFor(p => p.ModeloDocumentoId, p => p.Random.Int(min: 1))
            .RuleFor(p => p.NomeModelo, p => p.Random.Hash(255))
            .RuleFor(p => p.TextoDocumento, p => p.Random.Hash())
            .RuleFor(p => p.SetorId, setor.SetorId)
            .RuleFor(p => p.IsDeleted, p => false)
            .Generate();

        objetoParaExcluir = await _repository.InsertAsync(objetoParaExcluir, autoSave: true);

        // Act
        await _appService.DeleteAsync(objetoParaExcluir.ModeloDocumentoId);

        // Assert
        var objetoDeletado = await _repository.FindAsync(a => a.ModeloDocumentoId == objetoParaExcluir.ModeloDocumentoId);
        objetoDeletado.ShouldBeNull();
    }

    [Fact]
    public async Task Obter_ModeloDocumento_Por_Id_Deve_Passar()
    {
        // Arrange
        var setor = new TRF3.SISPREC.Setores.Setor
        {
            Nome = "Teste",
            Sigla = "FIN",
            Ativo = true
        };

        await _setorRepository.InsertAsync(setor, true);

        var objetoExistente = await _repository.FindAsync(a => a.ModeloDocumentoId == modeloDocumentoObj1!.ModeloDocumentoId);

        // Act
        var result = await _appService.GetAsync(objetoExistente!.ModeloDocumentoId);

        // Assert
        result.ShouldNotBeNull();
        result.ModeloDocumentoId.ShouldBe(objetoExistente.ModeloDocumentoId);
    }

    [Fact]
    public async Task Obter_ModeloDocumento_Com_Ordenacao_Padrao_Deve_Passar()
    {
        // Arrange
        var input = new ModeloDocumentoGetListInput();  // Sem filtros específicos para testar a ordenação padrão.

        // Act
        var result = await _appService.GetListAsync(input);

        // Assert
        result.Items.ShouldNotBeEmpty(); // Correção aplicada diretamente ao acessar a propriedade Items
        result.Items.OrderBy(l => l.ModeloDocumentoId).SequenceEqual(result.Items).ShouldBeTrue(); // Corrigido para usar OrderBy e SequenceEqual para verificar a ordem
    }
}