using System.Diagnostics.CodeAnalysis;
using TRF3.SISPREC.Web.Pages.AnaliseReinclusao.ViewModels;
using TRF3.SISPREC.Web.Pages.FilterInputs;

namespace TRF3.SISPREC.Web.Pages.AnaliseReinclusao
{
    [ExcludeFromCodeCoverage]
    public class IndexModel : SISPRECPageModel
    {
        public AnaliseReinclusaoViewModel? ViewModel { get; set; } = new();
        public AnaliseFilterInput? AnaliseReinclusaoFilterInput { get; set; }
        public async Task OnGetAsync()
        {
            await Task.CompletedTask;
        }
    }
}