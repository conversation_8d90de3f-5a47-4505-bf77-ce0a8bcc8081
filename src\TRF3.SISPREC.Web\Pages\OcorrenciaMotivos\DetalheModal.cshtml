@page
@using TRF3.SISPREC.Localization
@using Microsoft.AspNetCore.Mvc.Localization
@using TRF3.SISPREC.OcorrenciaMotivos
@using Volo.Abp.AspNetCore.Mvc.UI.Bootstrap.TagHelpers.Modal;
@model TRF3.SISPREC.Web.Pages.OcorrenciaMotivos.DetalheModalModel
@{
    Layout = null;
}

<form data-check-form-on-close="false">
    <abp-modal size="Large">
        <abp-modal-header title="Motivo Ocorrência"></abp-modal-header>
        <abp-modal-body>
            <abp-tabs>
                <abp-tab title="Detalhe">                
                    <abp-input asp-for="ViewModel.CodigoMotivo" readonly="true" />
                    <abp-input asp-for="ViewModel.DescricaoMotivo" readonly="true" />               
                    <abp-input asp-for="ViewModel.AcaoTipoDescricao" readonly="true" />
                    <abp-input asp-for="ViewModel.AnaliseTelaDescricao" readonly="true" />
                    <abp-input asp-for="ViewModel.Ativo" readonly="true" />
                </abp-tab>
                <abp-tab title="Histórico">
                    @await Component.InvokeAsync("Historico", new { nomeEntidade = typeof(OcorrenciaMotivo).FullName!, idEntidade = Model.ViewModel.OcorrenciaMotivoId.ToString() })
                </abp-tab>
            </abp-tabs>
        </abp-modal-body>
        <abp-modal-footer buttons="@(AbpModalButtons.Close)"></abp-modal-footer>
    </abp-modal>
</form>