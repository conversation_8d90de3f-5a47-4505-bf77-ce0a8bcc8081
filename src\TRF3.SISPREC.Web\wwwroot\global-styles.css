/* Your Global Styles */

/* 1. Define uma altura fixa para o abp-date-picker */
abp-date-picker {
    display: block; /* Garante que o elemento ocupe toda a largura disponível */
    height: 32.375px; /* Defina a altura desejada */
    box-sizing: border-box; /* Inclui padding e border no cálculo da altura */
    margin-bottom: 20px; /* Espaçamento opcional abaixo do date picker */
}

    /* 2. Ajusta a altura do .input-group interno para preencher o abp-date-picker */
    abp-date-picker .input-group {
        height: 100%;
        max-height: none !important; /* Remove o max-height inline existente */
    }

    /* 3. Ajusta a altura do input para preencher o contêiner */
    abp-date-picker .form-control {
        height: 100%;
        padding: 0.5rem; /* Ajusta o padding conforme necessário */
        font-size: 1rem; /* Ajusta o tamanho da fonte conforme necessário */
    }

    /* 4. Ajusta a altura dos botões para alinhar com o input */
    abp-date-picker .input-group .btn {
        height: 100%;
        padding: 0.5rem; /* Ajusta o padding conforme necessário */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 5. Garante que a mensagem de validação não afete o layout */
    abp-date-picker .field-validation-valid {
        display: block; /* Garante que o espaço para a mensagem seja reservado */
        min-height: 20px; /* Define uma altura mínima para evitar pulos no layout */
        margin-top: 5px; /* Espaçamento acima da mensagem */
        color: #dc3545; /* Cor de erro padrão do Bootstrap */
        font-size: 0.875rem; /* Tamanho da fonte da mensagem */
    }

    /* 6. Ajusta o input hidden (opcional) */
    abp-date-picker input[type="hidden"] {
        display: none; /* Garante que o input hidden não afete o layout */
    }


.select2-selection select2-selection--multiple .select2-search.select2-search--inline {
    width: 100% !important;
}

.select2-container .select2-selection--multiple {
    display: flex;
    align-items: center; /* Alinha os elementos ao centro verticalmente */
    flex-wrap: wrap; /* Permite que os itens selecionados quebrem para novas linhas */
    padding-right: 40px; /* Adiciona espaço para o botão de limpar */
}

    .select2-container .select2-selection--multiple .select2-selection__clear {
        position: absolute; /* Mantém o botão no mesmo local */
        right: 48px; /* Ajusta a posição horizontal */
        top: 50%; /* Centraliza verticalmente */
        transform: translateY(-50%); /* Ajusta a posição para o centro exato */
        display: flex;
        align-items: center; /* Centraliza o conteúdo do botão */
        justify-content: center;
        cursor: pointer;
    }


.select2-selection__choice__remove {
    padding: 0px 6px 2px 2px;
}

.select2-selection--multiple .select2-selection__choice {
    padding: 0px 6px;
    margin: 1px 2px !important;
    min-height: 25px;
    border-style: solid;
    border-radius: 6px;
    border-width: thin;
    border-color: var(--lpx-light);
    background-color: var(--lpx-card-bg);
}

.select2-results .select2-results__options .select2-results__option {
    padding: 5px 18px !important;
}



span.select2.select2-container.select2-container--bootstrap-5.select2-container--focus > span.selection > span,
span.select2.select2-container.select2-container--bootstrap-5 > span.selection > span {
    min-height: 32.375px !important;
    padding: 4px 60px 4px 12px !important;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    text-overflow: clip !important;
}

.select2-results__options {
    max-height: 300px !important;
    overflow-y: auto !important;
}


table {
    font-size: small;
}

#desktop-sidebar {
    font-size: small !important;
}

#menu-item-sair > a {
    padding: 0 10px 0 0 !important;
}

/* todos os inputs do tamanho pequeno */
.form-control, .input-group-text {
    height: calc(1.5em + 0.75rem + 2px) !important;
    padding: 0.375rem 0.5rem !important;
    font-size: 0.765625rem !important;
}

.form-select {
    /*padding: 0.675rem 3.75rem 0.675rem 1.25rem;*/
    padding-right: 1.25rem !important;
}

#lpx-wrapper > div.lpx-content-container > div.lpx-content > div.row > div > h1 {
    margin-top: 0 !important;
}

#lpx-sidebar > nav > div {
    padding-bottom: 15px !important;
    border-bottom-style: solid;
    border-bottom-width: thin;
    border-bottom-color: var(--lpx-light) !important;
}


#lpx-wrapper > div.lpx-content-container > div.lpx-content > div.card {
    margin-bottom: 0 !important;
}

@media screen and (min-width: 1400px) {
    .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
        max-width: 90% !important;
    }


    #main-navbar-collapse > ul:last-child {
        margin-left: 100px;
    }
}

@media screen and (max-width: 1399px) and (min-width: 800px) {
    .modal-dialog, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
        max-width: 95% !important;
    }
}

@media screen and (max-width: 799px) {
    .modal-dialog, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
        max-width: 100% !important;
    }
}


body {
    font-weight: 300; /* 300 é geralmente considerado como 'light'. Ajuste conforme necessário */
}

.dropdown-submenu .dropdown-menu {
    overflow: auto;
    max-height: 350px;
}

.card-header {
    padding-top: 6px;
    padding-bottom: 6px;
}

    .card-header .row .col a {
        margin-top: 3px;
    }

    .card-header .row .col {
        min-height: 30px;
    }

#AbpContentToolbar > div > div:nth-child(1) > h4 {
    margin-bottom: 2px !important;
}

#main-navbar {
    margin-bottom: 18px !important;
    min-height: 3rem !important;
    padding-top: 5px !important;
    padding-bottom: 5px !important;
}

.navbar-brand {
    padding-top: 0px;
    padding-bottom: 0px;
}

.btn.abp-action-button {
    --bs-btn-padding-y: 0.25rem;
    --bs-btn-padding-x: 0.5rem;
    --bs-btn-font-size: 0.875rem;
    --bs-btn-border-radius: var(--bs-border-radius-sm);
}


.custom-border {
    border: 1px solid rgba(0, 0, 0, 0.3) !important
}

.table-cadastro-justificativa {
    max-height: 200px !important;
    overflow: auto !important;
    width: 100%;
}

#app-version-footer {
    position: absolute !important;
    bottom: 0 !important;
    right: 0 !important;
    padding-right: 2em;
}

    #app-version-footer small {
        font-size: 0.70rem;
        color: var(--lpx-content-text);
        opacity: 0.8;
        font-weight: 100;
    }

#lpx-wrapper > div.lpx-content-container > div.lpx-content > div.card.mb-3 {
    margin-bottom: 5px !important;
}

.analise-sub-menu-borda-bottom span {
    border-bottom: var(--bs-border-width) var(--bs-border-style) #D0D7DC !important;
}

.destaque-amarelo {
    font-weight: bold;
    background-color: rgba(255, 255, 0, 0.3) !important;
}

.destaque-verde {
    font-weight: bold;
    background-color: #d0fdd7 !important;
}

.destaque-vermelho {
    font-weight: bold;
    background-color: #ff9ea2 !important;
}

.destaque-laranja {
    font-weight: bold;
    background-color: #ffe3a1 !important;
}