using TRF3.SISPREC.RequisicoesProtocolos.Dtos;
using Volo.Abp.Application.Services;

namespace TRF3.SISPREC.RequisicoesProtocolos;

public interface IRequisicaoProtocoloAppService : ICrudAppService<RequisicaoProtocoloDto, string, RequisicaoProtocoloGetListInput, CreateUpdateRequisicaoProtocoloDto, CreateUpdateRequisicaoProtocoloDto>
{
    Task<RequisicaoProtocoloTableDto> GetRequisicaoByIdAsync(string id);
}